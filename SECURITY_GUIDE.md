# Security Guide - VoIP Call Forwarding System

## 🔒 Security Overview

The VoIP Call Forwarding System implements multiple layers of security to protect user data, communications, and system integrity. This guide covers security implementations, best practices, and configuration guidelines.

## 🛡️ Security Architecture

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           Security Layers                                    │
├─────────────────┬─────────────────────────┬─────────────────────────────────┤
│  Application    │      Transport          │         Infrastructure         │
│  Security       │      Security           │         Security                │
│                 │                         │                                 │
│ • JWT Auth      │ • TLS/SSL Encryption    │ • Firewall Rules               │
│ • Input Valid   │ • WebRTC SRTP          │ • Network Segmentation         │
│ • Rate Limiting │ • Certificate Pinning   │ • Container Isolation          │
│ • CORS Policy   │ • HSTS Headers         │ • Access Controls              │
│ • API Keys      │ • Secure WebSockets    │ • Monitoring & Logging         │
└─────────────────┴─────────────────────────┴─────────────────────────────────┘
```

## 🔐 Authentication & Authorization

### JWT Token Security

#### Token Configuration
```typescript
// Backend JWT Configuration
const jwtConfig = {
  secret: process.env.JWT_SECRET, // 256-bit random key
  expiresIn: '24h',
  algorithm: 'HS256',
  issuer: 'voip-backend',
  audience: 'voip-clients'
};

const refreshTokenConfig = {
  secret: process.env.JWT_REFRESH_SECRET,
  expiresIn: '7d',
  algorithm: 'HS256'
};
```

#### Token Generation Best Practices
```typescript
// Secure token generation
import { randomBytes } from 'crypto';

export class AuthService {
  generateTokens(deviceId: string, deviceType: string) {
    const payload = {
      sub: deviceId,
      deviceType,
      iat: Math.floor(Date.now() / 1000),
      jti: randomBytes(16).toString('hex') // Unique token ID
    };

    const accessToken = jwt.sign(payload, jwtConfig.secret, {
      expiresIn: jwtConfig.expiresIn,
      algorithm: jwtConfig.algorithm,
      issuer: jwtConfig.issuer,
      audience: jwtConfig.audience
    });

    const refreshToken = jwt.sign(
      { sub: deviceId, type: 'refresh' },
      refreshTokenConfig.secret,
      { expiresIn: refreshTokenConfig.expiresIn }
    );

    return { accessToken, refreshToken };
  }
}
```

#### Token Validation
```typescript
// JWT validation middleware
@Injectable()
export class JwtAuthGuard implements CanActivate {
  constructor(private jwtService: JwtService) {}

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);

    if (!token) {
      throw new UnauthorizedException('Token not provided');
    }

    try {
      const payload = this.jwtService.verify(token, {
        secret: process.env.JWT_SECRET,
        algorithms: ['HS256'],
        issuer: 'voip-backend',
        audience: 'voip-clients'
      });

      request.user = payload;
      return true;
    } catch (error) {
      throw new UnauthorizedException('Invalid token');
    }
  }
}
```

### Device Authentication

#### Android Implementation
```java
public class AuthManager {
    private static final String TOKEN_KEY = "auth_token";
    private static final String REFRESH_TOKEN_KEY = "refresh_token";
    
    public void authenticateDevice(String deviceId, AuthCallback callback) {
        DeviceInfo deviceInfo = new DeviceInfo(
            deviceId,
            "android",
            Build.MODEL,
            Build.VERSION.RELEASE,
            getAppVersion()
        );
        
        apiService.authenticate(deviceInfo).enqueue(new Callback<AuthResponse>() {
            @Override
            public void onResponse(Call<AuthResponse> call, Response<AuthResponse> response) {
                if (response.isSuccessful() && response.body() != null) {
                    AuthResponse authResponse = response.body();
                    
                    // Store tokens securely
                    secureStorage.store(TOKEN_KEY, authResponse.getAccessToken());
                    secureStorage.store(REFRESH_TOKEN_KEY, authResponse.getRefreshToken());
                    
                    callback.onSuccess(authResponse);
                } else {
                    callback.onError(new AuthException("Authentication failed"));
                }
            }
        });
    }
}
```

#### iOS Implementation
```swift
class AuthManager {
    private let keychain = Keychain(service: "com.voipreceiver.auth")
    
    func authenticateDevice(deviceId: String, completion: @escaping (Result<AuthResponse, Error>) -> Void) {
        let deviceInfo = DeviceInfo(
            deviceId: deviceId,
            deviceType: "ios",
            deviceName: UIDevice.current.name,
            osVersion: UIDevice.current.systemVersion,
            appVersion: Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"
        )
        
        NetworkManager.shared.authenticate(deviceInfo: deviceInfo) { result in
            switch result {
            case .success(let authResponse):
                // Store tokens in Keychain
                self.keychain["access_token"] = authResponse.accessToken
                self.keychain["refresh_token"] = authResponse.refreshToken
                completion(.success(authResponse))
                
            case .failure(let error):
                completion(.failure(error))
            }
        }
    }
}
```

## 🔒 Data Encryption

### Database Encryption

#### Sensitive Data Encryption
```typescript
// Encrypt sensitive fields before storing
import { createCipher, createDecipher } from 'crypto';

@Entity('devices')
export class Device {
  @Column()
  deviceId: string;

  @Column({ transformer: new EncryptionTransformer() })
  sipPassword: string; // Encrypted in database

  @Column({ transformer: new EncryptionTransformer() })
  pushToken: string; // Encrypted in database
}

class EncryptionTransformer {
  private readonly algorithm = 'aes-256-cbc';
  private readonly key = process.env.ENCRYPTION_KEY;

  to(value: string): string {
    if (!value) return value;
    
    const cipher = createCipher(this.algorithm, this.key);
    let encrypted = cipher.update(value, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return encrypted;
  }

  from(value: string): string {
    if (!value) return value;
    
    const decipher = createDecipher(this.algorithm, this.key);
    let decrypted = decipher.update(value, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return decrypted;
  }
}
```

#### Database Connection Security
```typescript
// Secure database configuration
const databaseConfig = {
  type: 'postgres',
  host: process.env.DB_HOST,
  port: parseInt(process.env.DB_PORT),
  username: process.env.DB_USERNAME,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_DATABASE,
  ssl: {
    require: true,
    rejectUnauthorized: false, // Set to true in production with proper certificates
    ca: fs.readFileSync('path/to/ca-certificate.crt').toString(),
    key: fs.readFileSync('path/to/client-key.key').toString(),
    cert: fs.readFileSync('path/to/client-cert.crt').toString()
  },
  extra: {
    ssl: {
      require: true,
      rejectUnauthorized: true
    }
  }
};
```

### WebRTC Encryption

#### SRTP Configuration
```javascript
// WebRTC peer connection with encryption
const rtcConfiguration = {
  iceServers: [
    { urls: 'stun:stun.l.google.com:19302' },
    {
      urls: 'turn:your-turn-server.com:3478',
      username: 'username',
      credential: 'password'
    }
  ],
  // Enforce encryption
  bundlePolicy: 'max-bundle',
  rtcpMuxPolicy: 'require',
  iceCandidatePoolSize: 10
};

// Create peer connection with mandatory encryption
const peerConnection = new RTCPeerConnection(rtcConfiguration);

// Verify encryption is enabled
peerConnection.onconnectionstatechange = () => {
  if (peerConnection.connectionState === 'connected') {
    peerConnection.getStats().then(stats => {
      stats.forEach(report => {
        if (report.type === 'transport') {
          console.log('DTLS State:', report.dtlsState);
          console.log('SRTP Cipher:', report.srtpCipher);
        }
      });
    });
  }
};
```

## 🌐 Network Security

### TLS/SSL Configuration

#### Backend HTTPS Setup
```typescript
// Express HTTPS configuration
import * as https from 'https';
import * as fs from 'fs';

const httpsOptions = {
  key: fs.readFileSync('path/to/private-key.pem'),
  cert: fs.readFileSync('path/to/certificate.pem'),
  ca: fs.readFileSync('path/to/ca-bundle.pem'), // Certificate Authority bundle
  
  // Security options
  secureProtocol: 'TLSv1_2_method',
  ciphers: [
    'ECDHE-RSA-AES128-GCM-SHA256',
    'ECDHE-RSA-AES256-GCM-SHA384',
    'ECDHE-RSA-AES128-SHA256',
    'ECDHE-RSA-AES256-SHA384'
  ].join(':'),
  honorCipherOrder: true
};

const server = https.createServer(httpsOptions, app);
```

#### Certificate Pinning

##### iOS Certificate Pinning
```swift
import Alamofire

class NetworkManager {
    private let session: Session
    
    init() {
        let serverTrustManager = ServerTrustManager(
            evaluators: [
                "your-server.com": PinnedCertificatesTrustEvaluator(
                    certificates: [
                        // Load certificate from bundle
                        SecCertificateCreateWithData(nil, certificateData)!
                    ],
                    acceptSelfSignedCertificates: false,
                    performDefaultValidation: true,
                    validateHost: true
                )
            ]
        )
        
        self.session = Session(serverTrustManager: serverTrustManager)
    }
}
```

##### Android Certificate Pinning
```java
public class NetworkManager {
    private OkHttpClient createSecureClient() {
        CertificatePinner certificatePinner = new CertificatePinner.Builder()
            .add("your-server.com", "sha256/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=")
            .add("your-server.com", "sha256/BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB=") // Backup pin
            .build();
            
        return new OkHttpClient.Builder()
            .certificatePinner(certificatePinner)
            .connectionSpecs(Arrays.asList(
                ConnectionSpec.MODERN_TLS,
                ConnectionSpec.COMPATIBLE_TLS
            ))
            .build();
    }
}
```

### Secure WebSocket Configuration

#### Backend WebSocket Security
```typescript
@WebSocketGateway({
  namespace: '/webrtc',
  cors: {
    origin: process.env.ALLOWED_ORIGINS?.split(',') || ['https://your-app.com'],
    methods: ['GET', 'POST'],
    credentials: true
  },
  transports: ['websocket'], // Disable polling for security
  allowEIO3: false // Disable legacy protocol
})
export class WebRTCGateway {
  @UseGuards(WsJwtGuard)
  @SubscribeMessage('webrtc-signal')
  handleSignal(@ConnectedSocket() client: Socket, @MessageBody() data: any) {
    // Validate and sanitize data
    const validatedData = this.validateSignalData(data);
    
    // Process WebRTC signaling
    this.processSignal(client, validatedData);
  }
  
  private validateSignalData(data: any): SignalData {
    // Implement strict validation
    if (!data || typeof data !== 'object') {
      throw new WsException('Invalid signal data');
    }
    
    // Validate required fields
    const { type, callId, fromDevice, toDevice } = data;
    if (!type || !callId || !fromDevice || !toDevice) {
      throw new WsException('Missing required fields');
    }
    
    return data as SignalData;
  }
}
```

## 🔍 Input Validation & Sanitization

### API Input Validation

#### Backend Validation
```typescript
// DTO with validation decorators
import { IsString, IsEnum, IsOptional, Length, Matches } from 'class-validator';

export class RegisterDeviceDto {
  @IsString()
  @Length(1, 255)
  @Matches(/^[a-zA-Z0-9-_]+$/, { message: 'Device ID contains invalid characters' })
  deviceId: string;

  @IsEnum(['android', 'ios'])
  deviceType: 'android' | 'ios';

  @IsString()
  @Length(1, 255)
  deviceName: string;

  @IsOptional()
  @IsString()
  @Matches(/^[0-9a-f]{64}$/i, { message: 'Invalid push token format' })
  pushToken?: string;

  @IsOptional()
  @IsString()
  @Matches(/^\+?[1-9]\d{1,14}$/, { message: 'Invalid phone number format' })
  phoneNumber?: string;
}

// Controller with validation
@Controller('devices')
export class DevicesController {
  @Post('register')
  @UsePipes(new ValidationPipe({ whitelist: true, forbidNonWhitelisted: true }))
  async registerDevice(@Body() registerDeviceDto: RegisterDeviceDto) {
    // Input is automatically validated
    return this.devicesService.register(registerDeviceDto);
  }
}
```

#### SQL Injection Prevention
```typescript
// Use parameterized queries with TypeORM
@Repository()
export class DeviceRepository extends Repository<Device> {
  async findByDeviceId(deviceId: string): Promise<Device | null> {
    // TypeORM automatically prevents SQL injection
    return this.findOne({
      where: { deviceId },
      select: ['id', 'deviceId', 'deviceType', 'deviceName', 'status']
    });
  }
  
  async searchDevices(searchTerm: string): Promise<Device[]> {
    // Safe parameterized query
    return this.createQueryBuilder('device')
      .where('device.deviceName ILIKE :searchTerm', { 
        searchTerm: `%${searchTerm}%` 
      })
      .getMany();
  }
}
```

### Client-Side Validation

#### iOS Input Validation
```swift
class InputValidator {
    static func validatePhoneNumber(_ phoneNumber: String) -> Bool {
        let phoneRegex = "^\\+?[1-9]\\d{1,14}$"
        let phonePredicate = NSPredicate(format: "SELF MATCHES %@", phoneRegex)
        return phonePredicate.evaluate(with: phoneNumber)
    }
    
    static func validateDeviceId(_ deviceId: String) -> Bool {
        let deviceIdRegex = "^[a-zA-Z0-9-_]{1,255}$"
        let deviceIdPredicate = NSPredicate(format: "SELF MATCHES %@", deviceIdRegex)
        return deviceIdPredicate.evaluate(with: deviceId)
    }
    
    static func sanitizeInput(_ input: String) -> String {
        // Remove potentially dangerous characters
        let allowedCharacters = CharacterSet.alphanumerics.union(.whitespaces).union(CharacterSet(charactersIn: "-_"))
        return String(input.unicodeScalars.filter { allowedCharacters.contains($0) })
    }
}
```

#### Android Input Validation
```java
public class InputValidator {
    private static final Pattern PHONE_PATTERN = Pattern.compile("^\\+?[1-9]\\d{1,14}$");
    private static final Pattern DEVICE_ID_PATTERN = Pattern.compile("^[a-zA-Z0-9-_]{1,255}$");
    
    public static boolean isValidPhoneNumber(String phoneNumber) {
        return phoneNumber != null && PHONE_PATTERN.matcher(phoneNumber).matches();
    }
    
    public static boolean isValidDeviceId(String deviceId) {
        return deviceId != null && DEVICE_ID_PATTERN.matcher(deviceId).matches();
    }
    
    public static String sanitizeInput(String input) {
        if (input == null) return "";
        
        // Remove potentially dangerous characters
        return input.replaceAll("[^a-zA-Z0-9\\s\\-_]", "");
    }
}
```

## 🚫 Rate Limiting & DDoS Protection

### Backend Rate Limiting
```typescript
// Global rate limiting
import { ThrottlerModule, ThrottlerGuard } from '@nestjs/throttler';

@Module({
  imports: [
    ThrottlerModule.forRoot({
      ttl: 60, // Time window in seconds
      limit: 100, // Maximum requests per window
    }),
  ],
  providers: [
    {
      provide: APP_GUARD,
      useClass: ThrottlerGuard,
    },
  ],
})
export class AppModule {}

// Endpoint-specific rate limiting
@Controller('auth')
export class AuthController {
  @Post('authenticate')
  @Throttle(5, 60) // 5 requests per minute for authentication
  async authenticate(@Body() authDto: AuthenticateDto) {
    return this.authService.authenticate(authDto);
  }
}
```

### Client-Side Rate Limiting
```typescript
// Implement exponential backoff for failed requests
class ApiClient {
  private retryDelays = [1000, 2000, 4000, 8000, 16000]; // Exponential backoff
  
  async makeRequest(url: string, options: RequestOptions, retryCount = 0): Promise<Response> {
    try {
      const response = await fetch(url, options);
      
      if (response.status === 429) { // Rate limited
        if (retryCount < this.retryDelays.length) {
          const delay = this.retryDelays[retryCount];
          await this.sleep(delay);
          return this.makeRequest(url, options, retryCount + 1);
        }
        throw new Error('Rate limit exceeded');
      }
      
      return response;
    } catch (error) {
      if (retryCount < this.retryDelays.length) {
        const delay = this.retryDelays[retryCount];
        await this.sleep(delay);
        return this.makeRequest(url, options, retryCount + 1);
      }
      throw error;
    }
  }
  
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

## 🔐 Secure Storage

### iOS Keychain Storage
```swift
import Security

class SecureStorage {
    private let service = "com.voipreceiver.secure"
    
    func store(key: String, value: String) -> Bool {
        let data = value.data(using: .utf8)!
        
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecValueData as String: data,
            kSecAttrAccessible as String: kSecAttrAccessibleWhenUnlockedThisDeviceOnly
        ]
        
        // Delete existing item
        SecItemDelete(query as CFDictionary)
        
        // Add new item
        let status = SecItemAdd(query as CFDictionary, nil)
        return status == errSecSuccess
    }
    
    func retrieve(key: String) -> String? {
        let query: [String: Any] = [
            kSecClass as String: kSecClassGenericPassword,
            kSecAttrService as String: service,
            kSecAttrAccount as String: key,
            kSecReturnData as String: true,
            kSecMatchLimit as String: kSecMatchLimitOne
        ]
        
        var result: AnyObject?
        let status = SecItemCopyMatching(query as CFDictionary, &result)
        
        guard status == errSecSuccess,
              let data = result as? Data,
              let string = String(data: data, encoding: .utf8) else {
            return nil
        }
        
        return string
    }
}
```

### Android Encrypted SharedPreferences
```java
import androidx.security.crypto.EncryptedSharedPreferences;
import androidx.security.crypto.MasterKeys;

public class SecureStorage {
    private static final String PREFS_NAME = "secure_prefs";
    private SharedPreferences encryptedPrefs;
    
    public SecureStorage(Context context) throws Exception {
        String masterKeyAlias = MasterKeys.getOrCreate(MasterKeys.AES256_GCM_SPEC);
        
        encryptedPrefs = EncryptedSharedPreferences.create(
            PREFS_NAME,
            masterKeyAlias,
            context,
            EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
            EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
        );
    }
    
    public void store(String key, String value) {
        encryptedPrefs.edit()
            .putString(key, value)
            .apply();
    }
    
    public String retrieve(String key) {
        return encryptedPrefs.getString(key, null);
    }
    
    public void remove(String key) {
        encryptedPrefs.edit()
            .remove(key)
            .apply();
    }
}
```

## 📊 Security Monitoring & Logging

### Security Event Logging
```typescript
// Security event logger
@Injectable()
export class SecurityLogger {
  private readonly logger = new Logger(SecurityLogger.name);
  
  logAuthenticationAttempt(deviceId: string, success: boolean, ip: string) {
    const event = {
      type: 'AUTHENTICATION_ATTEMPT',
      deviceId,
      success,
      ip,
      timestamp: new Date().toISOString(),
      userAgent: this.getCurrentUserAgent()
    };
    
    if (success) {
      this.logger.log(`Authentication successful for device ${deviceId}`, event);
    } else {
      this.logger.warn(`Authentication failed for device ${deviceId}`, event);
    }
  }
  
  logSuspiciousActivity(deviceId: string, activity: string, details: any) {
    const event = {
      type: 'SUSPICIOUS_ACTIVITY',
      deviceId,
      activity,
      details,
      timestamp: new Date().toISOString(),
      severity: 'HIGH'
    };
    
    this.logger.error(`Suspicious activity detected: ${activity}`, event);
    
    // Trigger security alert
    this.triggerSecurityAlert(event);
  }
  
  private triggerSecurityAlert(event: any) {
    // Implement alerting mechanism (email, Slack, etc.)
    // This could integrate with monitoring systems like DataDog, New Relic, etc.
  }
}
```

### Intrusion Detection
```typescript
// Simple intrusion detection system
@Injectable()
export class IntrusionDetectionService {
  private failedAttempts = new Map<string, number>();
  private blockedIPs = new Set<string>();
  
  checkForBruteForce(ip: string, deviceId: string): boolean {
    const key = `${ip}:${deviceId}`;
    const attempts = this.failedAttempts.get(key) || 0;
    
    if (attempts >= 5) {
      this.blockedIPs.add(ip);
      this.securityLogger.logSuspiciousActivity(deviceId, 'BRUTE_FORCE_DETECTED', { ip, attempts });
      return false; // Block request
    }
    
    return true; // Allow request
  }
  
  recordFailedAttempt(ip: string, deviceId: string) {
    const key = `${ip}:${deviceId}`;
    const attempts = this.failedAttempts.get(key) || 0;
    this.failedAttempts.set(key, attempts + 1);
    
    // Clean up old entries after 1 hour
    setTimeout(() => {
      this.failedAttempts.delete(key);
    }, 3600000);
  }
  
  isBlocked(ip: string): boolean {
    return this.blockedIPs.has(ip);
  }
}
```

## 🔧 Security Configuration Checklist

### Backend Security Checklist
- [ ] JWT tokens use strong secrets (256-bit minimum)
- [ ] Database connections use SSL/TLS
- [ ] Sensitive data is encrypted at rest
- [ ] API endpoints have proper authentication
- [ ] Rate limiting is configured
- [ ] Input validation is implemented
- [ ] CORS is properly configured
- [ ] Security headers are set (HSTS, CSP, etc.)
- [ ] Logging captures security events
- [ ] Regular security updates are applied

### Mobile App Security Checklist
- [ ] Certificate pinning is implemented
- [ ] Sensitive data is stored securely (Keychain/EncryptedSharedPreferences)
- [ ] Network traffic is encrypted (HTTPS/WSS)
- [ ] Input validation is performed client-side
- [ ] Debug logging is disabled in production
- [ ] App transport security is configured
- [ ] Biometric authentication is available (optional)
- [ ] Session tokens are properly managed
- [ ] WebRTC uses SRTP encryption
- [ ] Push notifications don't contain sensitive data

### Infrastructure Security Checklist
- [ ] Firewall rules restrict unnecessary access
- [ ] Database access is limited to application servers
- [ ] SSL certificates are valid and up-to-date
- [ ] Server software is regularly updated
- [ ] Monitoring and alerting are configured
- [ ] Backup data is encrypted
- [ ] Access logs are maintained
- [ ] Intrusion detection is active
- [ ] Network segmentation is implemented
- [ ] Regular security audits are performed

---

**Security is an ongoing process.** Regularly review and update security measures, conduct security audits, and stay informed about new threats and vulnerabilities.
