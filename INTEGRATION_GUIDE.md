# Integration Guide - VoIP Call Forwarding System

## 🔗 Overview

This guide explains how to configure and integrate all three components of the VoIP Call Forwarding System to work together seamlessly. Follow these steps to ensure proper communication between the Android app, iOS app, and Backend service.

## 🏗️ Integration Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Android App   │    │  Backend Service│    │    iOS App      │
│                 │    │                 │    │                 │
│ Port: Dynamic   │◄──►│ Port: 3000      │◄──►│ Port: Dynamic   │
│ Protocol: HTTP  │    │ Protocol: HTTP  │    │ Protocol: HTTP  │
│ WebSocket: 3000 │    │ WebSocket: 3000 │    │ WebSocket: 3000 │
│                 │    │                 │    │                 │
│ Push: FCM       │    │ Push: APNs      │    │ Push: APNs      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │      PostgreSQL          │
                    │      Port: 5432          │
                    └─────────────────────────┘
```

## 🚀 Step-by-Step Integration

### Step 1: Backend Service Setup

#### 1.1 Environment Configuration
```bash
cd Backend

# Create production environment file
cat > .env << EOF
# Application Configuration
NODE_ENV=production
PORT=3000
APP_NAME=VoIP Backend
CORS_ORIGINS=*

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=voip_user
DB_PASSWORD=your_secure_password
DB_DATABASE=voip_db

# JWT Authentication
JWT_SECRET=$(openssl rand -base64 32)
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=$(openssl rand -base64 32)
JWT_REFRESH_EXPIRES_IN=7d

# Apple Push Notifications
APN_KEY_PATH=/app/apn-keys/AuthKey_XXXXXXXXXX.p8
APN_KEY_ID=XXXXXXXXXX
APN_TEAM_ID=XXXXXXXXXX
IOS_BUNDLE_ID=com.yourcompany.voipreceiver

# WebRTC Configuration
STUN_SERVERS=stun:stun.l.google.com:19302,stun:stun1.l.google.com:19302
TURN_SERVERS=turn:your-turn-server.com:3478
TURN_USERNAME=your_turn_username
TURN_PASSWORD=your_turn_password

# SIP Configuration
SIP_SERVER_HOST=localhost
SIP_SERVER_PORT=5060
SIP_DOMAIN=localhost
SIP_TRANSPORT=UDP

# Logging
LOG_LEVEL=info
LOG_FILE=logs/app.log
EOF
```

#### 1.2 Apple Push Notification Setup
```bash
# Create APN keys directory
mkdir -p apn-keys

# Copy your Apple Push Notification key file
cp ~/Downloads/AuthKey_XXXXXXXXXX.p8 apn-keys/
chmod 600 apn-keys/AuthKey_XXXXXXXXXX.p8

# Verify key file
ls -la apn-keys/
```

#### 1.3 Database Setup
```bash
# Start PostgreSQL
sudo systemctl start postgresql

# Create database and user
sudo -u postgres psql << EOF
CREATE DATABASE voip_db;
CREATE USER voip_user WITH ENCRYPTED PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE voip_db TO voip_user;
ALTER USER voip_user CREATEDB;
\q
EOF

# Run database migrations
npm run migration:run
```

#### 1.4 Start Backend Service
```bash
# Install dependencies
npm install

# Start the service
npm run start:prod

# Verify service is running
curl http://localhost:3000/health
```

### Step 2: iOS App Configuration

#### 2.1 Backend Connection Configuration
```swift
// In Config.swift or similar configuration file
struct AppConfig {
    static let backendBaseURL = "https://your-backend-server.com" // or http://localhost:3000 for local
    static let apiVersion = "v1"
    static let webSocketURL = "wss://your-backend-server.com/webrtc" // or ws://localhost:3000/webrtc
    
    // Derived URLs
    static let apiBaseURL = "\(backendBaseURL)/api/\(apiVersion)"
    static let deviceRegistrationURL = "\(apiBaseURL)/devices/register"
    static let authenticationURL = "\(apiBaseURL)/auth/authenticate"
}
```

#### 2.2 Network Configuration
```swift
// In NetworkManager.swift
import Alamofire

class NetworkManager {
    static let shared = NetworkManager()
    
    private let session: Session
    
    private init() {
        let configuration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = 30
        configuration.timeoutIntervalForResource = 60
        
        self.session = Session(configuration: configuration)
    }
    
    func registerDevice(deviceInfo: DeviceInfo, completion: @escaping (Result<DeviceResponse, Error>) -> Void) {
        let url = AppConfig.deviceRegistrationURL
        
        session.request(url, method: .post, parameters: deviceInfo, encoder: JSONParameterEncoder.default)
            .validate()
            .responseDecodable(of: DeviceResponse.self) { response in
                completion(response.result)
            }
    }
}
```

#### 2.3 WebSocket Configuration
```swift
// In WebSocketManager.swift
import SocketIO

class WebSocketManager {
    static let shared = WebSocketManager()
    
    private var manager: SocketManager!
    private var socket: SocketIOClient!
    
    private init() {
        setupSocket()
    }
    
    private func setupSocket() {
        guard let url = URL(string: AppConfig.backendBaseURL) else { return }
        
        manager = SocketManager(socketURL: url, config: [
            .log(false),
            .compress,
            .path("/webrtc/"),
            .reconnects(true),
            .reconnectAttempts(5),
            .reconnectWait(2)
        ])
        
        socket = manager.socket(forNamespace: "/webrtc")
        setupEventHandlers()
    }
    
    func connect(with token: String) {
        socket.connect(withPayload: ["token": token])
    }
}
```

#### 2.4 Push Notification Configuration
```swift
// In AppDelegate.swift
import PushKit

extension AppDelegate: PKPushRegistryDelegate {
    func setupVoIPPush() {
        let voipRegistry = PKPushRegistry(queue: DispatchQueue.main)
        voipRegistry.delegate = self
        voipRegistry.desiredPushTypes = [.voIP]
    }
    
    func pushRegistry(_ registry: PKPushRegistry, didUpdate pushCredentials: PKPushCredentials, for type: PKPushType) {
        guard type == .voIP else { return }
        
        let token = pushCredentials.token.map { String(format: "%02x", $0) }.joined()
        
        // Send token to backend
        NetworkManager.shared.updatePushToken(token) { result in
            switch result {
            case .success:
                print("Push token updated successfully")
            case .failure(let error):
                print("Failed to update push token: \(error)")
            }
        }
    }
}
```

### Step 3: Android App Configuration

#### 3.1 Backend Connection Configuration
```java
// In AppConfig.java
public class AppConfig {
    public static final String BACKEND_BASE_URL = "https://your-backend-server.com"; // or http://********:3000 for emulator
    public static final String API_VERSION = "v1";
    public static final String WEBSOCKET_URL = "wss://your-backend-server.com/webrtc"; // or ws://********:3000/webrtc
    
    // Derived URLs
    public static final String API_BASE_URL = BACKEND_BASE_URL + "/api/" + API_VERSION;
    public static final String DEVICE_REGISTRATION_URL = API_BASE_URL + "/devices/register";
    public static final String AUTHENTICATION_URL = API_BASE_URL + "/auth/authenticate";
    public static final String CALL_INITIATE_URL = API_BASE_URL + "/calls/initiate";
}
```

#### 3.2 Network Configuration
```java
// In NetworkManager.java
public class NetworkManager {
    private static NetworkManager instance;
    private Retrofit retrofit;
    private ApiService apiService;
    
    private NetworkManager() {
        OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .addInterceptor(new HttpLoggingInterceptor().setLevel(HttpLoggingInterceptor.Level.BODY))
            .build();
            
        retrofit = new Retrofit.Builder()
            .baseUrl(AppConfig.API_BASE_URL)
            .client(client)
            .addConverterFactory(GsonConverterFactory.create())
            .build();
            
        apiService = retrofit.create(ApiService.class);
    }
    
    public static synchronized NetworkManager getInstance() {
        if (instance == null) {
            instance = new NetworkManager();
        }
        return instance;
    }
    
    public void registerDevice(DeviceInfo deviceInfo, Callback<DeviceResponse> callback) {
        Call<DeviceResponse> call = apiService.registerDevice(deviceInfo);
        call.enqueue(callback);
    }
}
```

#### 3.3 WebSocket Configuration
```java
// In WebSocketManager.java
import io.socket.client.IO;
import io.socket.client.Socket;

public class WebSocketManager {
    private static WebSocketManager instance;
    private Socket socket;
    
    private WebSocketManager() {
        try {
            IO.Options options = new IO.Options();
            options.path = "/webrtc/";
            options.reconnection = true;
            options.reconnectionAttempts = 5;
            options.reconnectionDelay = 2000;
            
            socket = IO.socket(AppConfig.BACKEND_BASE_URL, options);
            setupEventHandlers();
        } catch (URISyntaxException e) {
            e.printStackTrace();
        }
    }
    
    public static synchronized WebSocketManager getInstance() {
        if (instance == null) {
            instance = new WebSocketManager();
        }
        return instance;
    }
    
    public void connect(String token) {
        socket.io().on(Manager.EVENT_TRANSPORT, args -> {
            Transport transport = (Transport) args[0];
            transport.on(Transport.EVENT_REQUEST_HEADERS, args1 -> {
                Map<String, List<String>> headers = (Map<String, List<String>>) args1[0];
                headers.put("Authorization", Arrays.asList("Bearer " + token));
            });
        });
        
        socket.connect();
    }
}
```

### Step 4: Component Integration Testing

#### 4.1 Backend Health Check
```bash
# Test backend service
curl -X GET http://localhost:3000/health

# Expected response:
{
  "status": "ok",
  "timestamp": "2023-12-07T10:30:00Z",
  "uptime": 3600,
  "environment": "production"
}
```

#### 4.2 Device Registration Test
```bash
# Test Android device registration
curl -X POST http://localhost:3000/api/v1/devices/register \
  -H "Content-Type: application/json" \
  -d '{
    "deviceId": "android-test-123",
    "deviceType": "android",
    "deviceName": "Test Android Device",
    "osVersion": "Android 12",
    "appVersion": "1.0.0"
  }'

# Test iOS device registration
curl -X POST http://localhost:3000/api/v1/devices/register \
  -H "Content-Type: application/json" \
  -d '{
    "deviceId": "ios-test-456",
    "deviceType": "ios",
    "deviceName": "Test iPhone",
    "osVersion": "iOS 16.0",
    "appVersion": "1.0.0",
    "pushToken": "test-push-token-here"
  }'
```

#### 4.3 Device Pairing Test
```bash
# First authenticate to get JWT token
TOKEN=$(curl -X POST http://localhost:3000/api/v1/auth/authenticate \
  -H "Content-Type: application/json" \
  -d '{"deviceId": "android-test-123"}' \
  | jq -r '.access_token')

# Test device pairing
curl -X POST http://localhost:3000/api/v1/devices/pair \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "androidDeviceId": "android-test-123",
    "iosDeviceId": "ios-test-456"
  }'
```

#### 4.4 WebSocket Connection Test
```javascript
// Test WebSocket connection (run in browser console or Node.js)
const io = require('socket.io-client');

const socket = io('http://localhost:3000/webrtc', {
  auth: {
    token: 'your-jwt-token-here'
  }
});

socket.on('connect', () => {
  console.log('Connected to WebSocket');
});

socket.on('disconnect', () => {
  console.log('Disconnected from WebSocket');
});

socket.on('webrtc-config', (config) => {
  console.log('Received WebRTC config:', config);
});
```

### Step 5: End-to-End Integration Test

#### 5.1 Complete Call Flow Test
```bash
# 1. Register both devices (already done above)

# 2. Pair devices (already done above)

# 3. Initiate a test call
curl -X POST http://localhost:3000/api/v1/calls/initiate \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "androidDeviceId": "android-test-123",
    "callerId": "+**********",
    "callerName": "Test Caller"
  }'

# 4. Check call status
CALL_ID="returned-call-id-from-step-3"
curl -X GET http://localhost:3000/api/v1/calls/$CALL_ID \
  -H "Authorization: Bearer $TOKEN"
```

#### 5.2 Push Notification Test
```bash
# Test VoIP push notification
curl -X POST http://localhost:3000/api/v1/push/voip-call \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{
    "deviceId": "ios-test-456",
    "callerId": "+**********",
    "callerName": "Test Caller",
    "callUuid": "test-call-uuid"
  }'
```

## 🔧 Configuration Files

### Backend Configuration Template
```yaml
# docker-compose.yml for complete system
version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: voip_db
      POSTGRES_USER: voip_user
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  voip-backend:
    build: ./Backend
    environment:
      NODE_ENV: production
      DB_HOST: postgres
      REDIS_HOST: redis
      JWT_SECRET: ${JWT_SECRET}
      APN_KEY_PATH: /app/apn-keys/AuthKey_${APN_KEY_ID}.p8
      APN_KEY_ID: ${APN_KEY_ID}
      APN_TEAM_ID: ${APN_TEAM_ID}
    ports:
      - "3000:3000"
    volumes:
      - ./apn-keys:/app/apn-keys:ro
    depends_on:
      - postgres
      - redis

volumes:
  postgres_data:
  redis_data:
```

### iOS Configuration Template
```swift
// Config.swift
struct Config {
    struct Backend {
        static let baseURL = "https://your-backend-server.com"
        static let apiVersion = "v1"
        static let webSocketNamespace = "/webrtc"
    }
    
    struct VoIP {
        static let bundleId = "com.yourcompany.voipreceiver"
        static let pushType = "voip"
    }
    
    struct WebRTC {
        static let stunServers = [
            "stun:stun.l.google.com:19302",
            "stun:stun1.l.google.com:19302"
        ]
        static let turnServers = [
            "turn:your-turn-server.com:3478"
        ]
    }
}
```

### Android Configuration Template
```xml
<!-- strings.xml -->
<resources>
    <string name="app_name">VoIP Forwarder</string>
    <string name="backend_base_url">https://your-backend-server.com</string>
    <string name="api_version">v1</string>
    <string name="websocket_namespace">/webrtc</string>
</resources>
```

## 🔍 Troubleshooting Integration Issues

### Common Integration Problems

#### 1. Backend Connection Issues
```bash
# Check if backend is accessible
curl -v http://localhost:3000/health

# Check firewall rules
sudo ufw status
sudo iptables -L

# Check if port is open
netstat -tlnp | grep :3000
```

#### 2. Database Connection Issues
```bash
# Test database connection
psql -h localhost -U voip_user -d voip_db -c "SELECT version();"

# Check database logs
sudo tail -f /var/log/postgresql/postgresql-*.log
```

#### 3. Push Notification Issues
```bash
# Verify APN key file
openssl pkcs8 -nocrypt -in AuthKey_XXXXXXXXXX.p8 -inform PEM

# Test push notification manually
curl -X POST http://localhost:3000/api/v1/push/voip-call \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"deviceId": "ios-device-id", "callerId": "+**********"}'
```

#### 4. WebSocket Connection Issues
```javascript
// Debug WebSocket connection
const socket = io('http://localhost:3000/webrtc', {
  auth: { token: 'your-token' },
  transports: ['websocket'],
  upgrade: false
});

socket.on('connect_error', (error) => {
  console.error('Connection error:', error);
});
```

### Integration Verification Checklist

- [ ] Backend service starts without errors
- [ ] Database migrations run successfully
- [ ] Android app connects to backend
- [ ] iOS app connects to backend
- [ ] Device registration works for both platforms
- [ ] Device pairing completes successfully
- [ ] WebSocket connections establish
- [ ] Push notifications are received on iOS
- [ ] Call initiation triggers push notification
- [ ] WebRTC signaling works between devices
- [ ] Audio session establishes successfully
- [ ] Call quality metrics are reported
- [ ] Call history is stored correctly

## 🚀 Production Deployment Integration

### Environment-Specific Configuration

#### Development Environment
```bash
# Backend
NODE_ENV=development
BACKEND_URL=http://localhost:3000

# iOS
let backendURL = "http://localhost:3000"

# Android
String BACKEND_URL = "http://********:3000"; // For emulator
```

#### Staging Environment
```bash
# Backend
NODE_ENV=staging
BACKEND_URL=https://staging-api.yourcompany.com

# iOS
let backendURL = "https://staging-api.yourcompany.com"

# Android
String BACKEND_URL = "https://staging-api.yourcompany.com";
```

#### Production Environment
```bash
# Backend
NODE_ENV=production
BACKEND_URL=https://api.yourcompany.com

# iOS
let backendURL = "https://api.yourcompany.com"

# Android
String BACKEND_URL = "https://api.yourcompany.com";
```

---

**Integration completed successfully?** Your VoIP Call Forwarding System is now ready for use! Check the [User Guide](USER_GUIDE.md) for end-user instructions.
