# Troubleshooting Guide - VoIP Call Forwarding System

## 🔍 Common Issues and Solutions

### 📱 Android App Issues

#### Issue: App Cannot Detect Incoming Calls
**Symptoms:**
- Incoming calls are not being detected
- No call forwarding occurs
- App shows "Call detection inactive"

**Solutions:**
1. **Check Permissions:**
   ```bash
   # Verify required permissions are granted
   adb shell dumpsys package com.voipforwarding.android | grep permission
   ```
   - Phone permission
   - Microphone permission
   - Network access permission

2. **Verify Phone State Listener:**
   ```java
   // Check if TelephonyManager is properly initialized
   TelephonyManager telephonyManager = (TelephonyManager) getSystemService(Context.TELEPHONY_SERVICE);
   if (telephonyManager != null) {
       telephonyManager.listen(phoneStateListener, PhoneStateListener.LISTEN_CALL_STATE);
   }
   ```

3. **Check Background Restrictions:**
   - Go to Settings → Apps → VoIP Forwarder → Battery
   - Disable battery optimization
   - Allow background activity

4. **Verify Broadcast Receiver:**
   ```xml
   <!-- Ensure receiver is declared in AndroidManifest.xml -->
   <receiver android:name=".receivers.CallReceiver"
             android:enabled="true"
             android:exported="true">
       <intent-filter android:priority="1000">
           <action android:name="android.intent.action.PHONE_STATE" />
       </intent-filter>
   </receiver>
   ```

#### Issue: Cannot Connect to Backend Server
**Symptoms:**
- "Connection failed" error messages
- Device registration fails
- Network timeout errors

**Solutions:**
1. **Check Network Configuration:**
   ```java
   // Verify backend URL is correct
   public static final String BACKEND_BASE_URL = "https://your-server.com";
   
   // For local development
   public static final String BACKEND_BASE_URL = "http://********:3000"; // Emulator
   public static final String BACKEND_BASE_URL = "http://*************:3000"; // Physical device
   ```

2. **Test Network Connectivity:**
   ```bash
   # Test from device browser
   # Navigate to: http://your-server.com:3000/health
   
   # Or use ADB
   adb shell am start -a android.intent.action.VIEW -d "http://your-server.com:3000/health"
   ```

3. **Check Network Security Config:**
   ```xml
   <!-- In AndroidManifest.xml for HTTP connections -->
   <application android:usesCleartextTraffic="true">
   
   <!-- Or configure network_security_config.xml -->
   <application android:networkSecurityConfig="@xml/network_security_config">
   ```

4. **Verify SSL/TLS Configuration:**
   ```java
   // For HTTPS connections, ensure proper certificate validation
   OkHttpClient client = new OkHttpClient.Builder()
       .certificatePinner(new CertificatePinner.Builder()
           .add("your-server.com", "sha256/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=")
           .build())
       .build();
   ```

#### Issue: WebRTC Connection Fails
**Symptoms:**
- Audio connection cannot be established
- WebRTC peer connection fails
- ICE connection state remains "checking"

**Solutions:**
1. **Check STUN/TURN Configuration:**
   ```java
   List<PeerConnection.IceServer> iceServers = new ArrayList<>();
   iceServers.add(PeerConnection.IceServer.builder("stun:stun.l.google.com:19302").build());
   iceServers.add(PeerConnection.IceServer.builder("turn:your-turn-server.com:3478")
       .setUsername("username")
       .setPassword("password")
       .build());
   ```

2. **Verify Network Connectivity:**
   ```bash
   # Test STUN server connectivity
   adb shell ping stun.l.google.com
   
   # Check if UDP traffic is allowed
   # Ensure firewall allows UDP traffic on required ports
   ```

3. **Debug ICE Candidates:**
   ```java
   peerConnection.addObserver(new PeerConnection.Observer() {
       @Override
       public void onIceCandidate(IceCandidate iceCandidate) {
           Log.d("WebRTC", "ICE Candidate: " + iceCandidate.toString());
           // Send candidate to remote peer via signaling server
       }
   });
   ```

### 🍎 iOS App Issues

#### Issue: VoIP Push Notifications Not Received
**Symptoms:**
- iPhone doesn't wake up for incoming calls
- No CallKit interface appears
- Push notifications fail silently

**Solutions:**
1. **Verify Push Certificate Configuration:**
   ```bash
   # Check if VoIP Services certificate is valid
   openssl x509 -in voip_services.pem -text -noout
   
   # Verify certificate expiration date
   openssl x509 -in voip_services.pem -noout -dates
   ```

2. **Check Push Token Registration:**
   ```swift
   func pushRegistry(_ registry: PKPushRegistry, didUpdate pushCredentials: PKPushCredentials, for type: PKPushType) {
       guard type == .voIP else { return }
       
       let token = pushCredentials.token.map { String(format: "%02x", $0) }.joined()
       print("VoIP Push Token: \(token)")
       
       // Ensure token is sent to backend server
       NetworkManager.shared.updatePushToken(token) { result in
           print("Token update result: \(result)")
       }
   }
   ```

3. **Verify Background Modes:**
   ```xml
   <!-- In Info.plist -->
   <key>UIBackgroundModes</key>
   <array>
       <string>voip</string>
       <string>audio</string>
       <string>background-processing</string>
   </array>
   ```

4. **Test Push Notification Manually:**
   ```bash
   # Use APNs HTTP/2 API to test push notifications
   curl -v \
     -d '{"aps":{"alert":"Test VoIP Call","sound":"default"},"caller_id":"+1234567890"}' \
     -H "apns-topic: com.yourapp.voipreceiver.voip" \
     -H "apns-push-type: voip" \
     -H "apns-priority: 10" \
     -H "authorization: bearer $JWT_TOKEN" \
     --http2 \
     https://api.push.apple.com/3/device/$DEVICE_TOKEN
   ```

#### Issue: CallKit Integration Not Working
**Symptoms:**
- Calls don't appear in native iOS call interface
- Call actions (answer/decline) don't work
- Call history doesn't sync with Phone app

**Solutions:**
1. **Verify CallKit Configuration:**
   ```swift
   import CallKit
   
   class CallKitManager: NSObject {
       private let callController = CXCallController()
       private let provider: CXProvider
       
       override init() {
           let configuration = CXProviderConfiguration(localizedName: "VoIP Receiver")
           configuration.supportsVideo = false
           configuration.maximumCallsPerCallGroup = 1
           configuration.supportedHandleTypes = [.phoneNumber]
           
           provider = CXProvider(configuration: configuration)
           super.init()
           provider.setDelegate(self, queue: nil)
       }
   }
   ```

2. **Check Provider Delegate Implementation:**
   ```swift
   extension CallKitManager: CXProviderDelegate {
       func providerDidReset(_ provider: CXProvider) {
           // Handle provider reset
       }
       
       func provider(_ provider: CXProvider, perform action: CXAnswerCallAction) {
           // Handle call answer
           action.fulfill()
       }
       
       func provider(_ provider: CXProvider, perform action: CXEndCallAction) {
           // Handle call end
           action.fulfill()
       }
   }
   ```

3. **Verify Call Reporting:**
   ```swift
   func reportIncomingCall(uuid: UUID, handle: String, completion: @escaping (Error?) -> Void) {
       let update = CXCallUpdate()
       update.remoteHandle = CXHandle(type: .phoneNumber, value: handle)
       update.hasVideo = false
       update.localizedCallerName = "Incoming Call"
       
       provider.reportNewIncomingCall(with: uuid, update: update) { error in
           completion(error)
       }
   }
   ```

#### Issue: Audio Session Problems
**Symptoms:**
- No audio during calls
- Audio routing issues (speaker/earpiece)
- Audio interruptions not handled properly

**Solutions:**
1. **Configure Audio Session:**
   ```swift
   import AVFoundation
   
   func configureAudioSession() {
       let audioSession = AVAudioSession.sharedInstance()
       
       do {
           try audioSession.setCategory(.playAndRecord, 
                                      mode: .voiceChat, 
                                      options: [.allowBluetooth, .allowBluetoothA2DP])
           try audioSession.setActive(true)
       } catch {
           print("Audio session configuration failed: \(error)")
       }
   }
   ```

2. **Handle Audio Route Changes:**
   ```swift
   NotificationCenter.default.addObserver(
       forName: AVAudioSession.routeChangeNotification,
       object: nil,
       queue: .main
   ) { notification in
       guard let userInfo = notification.userInfo,
             let reasonValue = userInfo[AVAudioSessionRouteChangeReasonKey] as? UInt,
             let reason = AVAudioSession.RouteChangeReason(rawValue: reasonValue) else {
           return
       }
       
       switch reason {
       case .newDeviceAvailable:
           // Handle new audio device
           break
       case .oldDeviceUnavailable:
           // Handle audio device removal
           break
       default:
           break
       }
   }
   ```

3. **Handle Audio Interruptions:**
   ```swift
   NotificationCenter.default.addObserver(
       forName: AVAudioSession.interruptionNotification,
       object: nil,
       queue: .main
   ) { notification in
       guard let userInfo = notification.userInfo,
             let typeValue = userInfo[AVAudioSessionInterruptionTypeKey] as? UInt,
             let type = AVAudioSession.InterruptionType(rawValue: typeValue) else {
           return
       }
       
       switch type {
       case .began:
           // Handle interruption began
           break
       case .ended:
           // Handle interruption ended
           if let optionsValue = userInfo[AVAudioSessionInterruptionOptionKey] as? UInt {
               let options = AVAudioSession.InterruptionOptions(rawValue: optionsValue)
               if options.contains(.shouldResume) {
                   // Resume audio session
               }
           }
           break
       @unknown default:
           break
       }
   }
   ```

### 🖥️ Backend Service Issues

#### Issue: Database Connection Failures
**Symptoms:**
- "Connection refused" errors
- Database queries timeout
- Migration failures

**Solutions:**
1. **Check Database Service:**
   ```bash
   # Check if PostgreSQL is running
   sudo systemctl status postgresql
   
   # Start PostgreSQL if not running
   sudo systemctl start postgresql
   
   # Check database connectivity
   psql -h localhost -U voip_user -d voip_db -c "SELECT version();"
   ```

2. **Verify Database Configuration:**
   ```bash
   # Check environment variables
   echo $DB_HOST
   echo $DB_PORT
   echo $DB_USERNAME
   echo $DB_DATABASE
   
   # Test connection with environment variables
   psql -h $DB_HOST -p $DB_PORT -U $DB_USERNAME -d $DB_DATABASE
   ```

3. **Check Database Logs:**
   ```bash
   # View PostgreSQL logs
   sudo tail -f /var/log/postgresql/postgresql-*.log
   
   # Check for connection errors
   grep "connection" /var/log/postgresql/postgresql-*.log
   ```

4. **Verify Database Permissions:**
   ```sql
   -- Connect as postgres user
   sudo -u postgres psql
   
   -- Check user permissions
   \du voip_user
   
   -- Grant necessary permissions
   GRANT ALL PRIVILEGES ON DATABASE voip_db TO voip_user;
   GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO voip_user;
   GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO voip_user;
   ```

#### Issue: Apple Push Notification Failures
**Symptoms:**
- Push notifications not delivered
- APNs authentication errors
- Invalid push token errors

**Solutions:**
1. **Verify APNs Configuration:**
   ```bash
   # Check if APNs key file exists and has correct permissions
   ls -la apn-keys/AuthKey_*.p8
   chmod 600 apn-keys/AuthKey_*.p8
   
   # Verify key file format
   openssl pkcs8 -nocrypt -in apn-keys/AuthKey_*.p8 -inform PEM
   ```

2. **Check Environment Variables:**
   ```bash
   echo $APN_KEY_PATH
   echo $APN_KEY_ID
   echo $APN_TEAM_ID
   echo $IOS_BUNDLE_ID
   ```

3. **Test APNs Connection:**
   ```javascript
   // In Node.js console or test script
   const apn = require('node-apn');
   
   const options = {
     token: {
       key: process.env.APN_KEY_PATH,
       keyId: process.env.APN_KEY_ID,
       teamId: process.env.APN_TEAM_ID
     },
     production: false // Set to true for production
   };
   
   const apnProvider = new apn.Provider(options);
   
   // Test connection
   apnProvider.client.on('connected', () => {
     console.log('Connected to APNs');
   });
   
   apnProvider.client.on('error', (error) => {
     console.error('APNs connection error:', error);
   });
   ```

4. **Validate Push Tokens:**
   ```javascript
   // Check if push token format is valid
   function isValidPushToken(token) {
     return /^[0-9a-f]{64}$/i.test(token);
   }
   
   // Log push token validation
   console.log('Push token valid:', isValidPushToken(devicePushToken));
   ```

#### Issue: WebSocket Connection Problems
**Symptoms:**
- WebSocket connections fail to establish
- Frequent disconnections
- Signaling messages not delivered

**Solutions:**
1. **Check WebSocket Configuration:**
   ```typescript
   // In WebRTC Gateway
   @WebSocketGateway({
     namespace: '/webrtc',
     cors: {
       origin: '*',
       methods: ['GET', 'POST']
     },
     transports: ['websocket']
   })
   export class WebRTCGateway {
     // Gateway implementation
   }
   ```

2. **Debug WebSocket Events:**
   ```typescript
   @SubscribeMessage('connection')
   handleConnection(@ConnectedSocket() client: Socket) {
     console.log(`Client connected: ${client.id}`);
     
     client.on('disconnect', (reason) => {
       console.log(`Client disconnected: ${client.id}, reason: ${reason}`);
     });
     
     client.on('error', (error) => {
       console.error(`WebSocket error for client ${client.id}:`, error);
     });
   }
   ```

3. **Check Reverse Proxy Configuration:**
   ```nginx
   # Nginx configuration for WebSocket
   location /socket.io/ {
       proxy_pass http://backend:3000;
       proxy_http_version 1.1;
       proxy_set_header Upgrade $http_upgrade;
       proxy_set_header Connection "upgrade";
       proxy_set_header Host $host;
       proxy_set_header X-Real-IP $remote_addr;
       proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
       proxy_set_header X-Forwarded-Proto $scheme;
   }
   ```

### 🔧 System-Wide Issues

#### Issue: Poor Call Quality
**Symptoms:**
- Audio is choppy or delayed
- Frequent call drops
- Echo or feedback

**Solutions:**
1. **Check Network Conditions:**
   ```bash
   # Test network latency
   ping -c 10 your-server.com
   
   # Test bandwidth
   speedtest-cli
   
   # Check packet loss
   mtr your-server.com
   ```

2. **Optimize WebRTC Configuration:**
   ```javascript
   const rtcConfiguration = {
     iceServers: [
       { urls: 'stun:stun.l.google.com:19302' },
       {
         urls: 'turn:your-turn-server.com:3478',
         username: 'username',
         credential: 'password'
       }
     ],
     iceCandidatePoolSize: 10,
     bundlePolicy: 'max-bundle',
     rtcpMuxPolicy: 'require'
   };
   ```

3. **Adjust Audio Codec Settings:**
   ```javascript
   // Prefer OPUS codec for better quality
   const audioCodecs = ['opus', 'PCMU', 'PCMA'];
   
   // Set audio constraints
   const audioConstraints = {
     audio: {
       echoCancellation: true,
       noiseSuppression: true,
       autoGainControl: true,
       sampleRate: 48000,
       channelCount: 1
     }
   };
   ```

#### Issue: High Battery Drain
**Symptoms:**
- Rapid battery consumption on mobile devices
- Device heating during calls
- Background app consuming resources

**Solutions:**
1. **Optimize Background Processing:**
   ```swift
   // iOS: Use background tasks efficiently
   var backgroundTask: UIBackgroundTaskIdentifier = .invalid
   
   func startBackgroundTask() {
       backgroundTask = UIApplication.shared.beginBackgroundTask {
           self.endBackgroundTask()
       }
   }
   
   func endBackgroundTask() {
       if backgroundTask != .invalid {
           UIApplication.shared.endBackgroundTask(backgroundTask)
           backgroundTask = .invalid
       }
   }
   ```

2. **Reduce Network Activity:**
   ```java
   // Android: Optimize network requests
   // Use connection pooling
   OkHttpClient client = new OkHttpClient.Builder()
       .connectionPool(new ConnectionPool(5, 5, TimeUnit.MINUTES))
       .build();
   
   // Batch API requests when possible
   // Use WebSocket for real-time communication instead of polling
   ```

3. **Monitor Resource Usage:**
   ```bash
   # Monitor CPU and memory usage
   adb shell top -p $(adb shell pidof com.voipforwarding.android)
   
   # Check battery usage
   adb shell dumpsys batterystats | grep com.voipforwarding.android
   ```

## 📊 Diagnostic Tools

### Backend Diagnostics
```bash
# Check service health
curl http://localhost:3000/health

# View application logs
tail -f logs/app.log

# Monitor database connections
psql -d voip_db -c "SELECT * FROM pg_stat_activity;"

# Check WebSocket connections
netstat -an | grep :3000
```

### iOS Diagnostics
```swift
// Enable detailed logging
#if DEBUG
    let logLevel: LogLevel = .verbose
#else
    let logLevel: LogLevel = .error
#endif

// Monitor memory usage
func logMemoryUsage() {
    let memoryUsage = mach_task_basic_info()
    print("Memory usage: \(memoryUsage.resident_size / 1024 / 1024) MB")
}

// Monitor network requests
URLProtocol.registerClass(NetworkLoggingProtocol.self)
```

### Android Diagnostics
```java
// Enable debug logging
if (BuildConfig.DEBUG) {
    HttpLoggingInterceptor logging = new HttpLoggingInterceptor();
    logging.setLevel(HttpLoggingInterceptor.Level.BODY);
    okHttpClient.addInterceptor(logging);
}

// Monitor network connectivity
ConnectivityManager cm = (ConnectivityManager) getSystemService(Context.CONNECTIVITY_SERVICE);
NetworkInfo activeNetwork = cm.getActiveNetworkInfo();
boolean isConnected = activeNetwork != null && activeNetwork.isConnectedOrConnecting();
```

---

**Still experiencing issues?** Contact our support team with the diagnostic information collected using the tools above.
