# Backend Integration Guide

This guide explains how to integrate the iOS VoIP Receiver app with your backend server to enable call forwarding from Android devices.

## Architecture Overview

```
[Android Phone] → [Backend Server] → [iOS VoIP App]
     (SIM)           (Bridge/Proxy)      (No SIM)
```

The backend server acts as a bridge that:
1. Receives calls from the Android phone
2. Sends VoIP push notifications to iOS
3. Establishes SIP/WebRTC connection with iOS app
4. Routes audio between Android and iOS

## Backend Requirements

### 1. VoIP Push Notification Service

Your backend must send VoIP push notifications to wake the iOS app:

```python
# Python example using APNs
import jwt
import requests
from datetime import datetime, timedelta

def send_voip_push(device_token, caller_id, caller_name=None):
    # JWT token for APNs authentication
    payload = {
        'iss': 'YOUR_TEAM_ID',
        'iat': datetime.utcnow(),
        'exp': datetime.utcnow() + timedelta(hours=1)
    }
    
    headers = {
        'alg': 'ES256',
        'kid': 'YOUR_KEY_ID'
    }
    
    token = jwt.encode(payload, private_key, algorithm='ES256', headers=headers)
    
    # VoIP push payload
    push_payload = {
        'aps': {
            'alert': f'Incoming call from {caller_name or caller_id}',
            'sound': 'default'
        },
        'caller_id': caller_id,
        'caller_name': caller_name,
        'call_uuid': str(uuid.uuid4()),
        'server_info': {
            'sip_server': 'your-sip-server.com',
            'port': 5060,
            'transport': 'UDP'
        }
    }
    
    headers = {
        'authorization': f'bearer {token}',
        'apns-topic': f'{bundle_id}.voip',
        'apns-push-type': 'voip',
        'apns-priority': '10'
    }
    
    response = requests.post(
        f'https://api.push.apple.com/3/device/{device_token}',
        json=push_payload,
        headers=headers
    )
    
    return response.status_code == 200
```

### 2. SIP Server Configuration

Configure your SIP server to handle iOS registrations:

```bash
# Example Asterisk configuration
# /etc/asterisk/sip.conf

[general]
context=default
allowoverlap=no
udpbindaddr=0.0.0.0:5060
tcpenable=yes
tcpbindaddr=0.0.0.0:5060
transport=udp,tcp,tls,ws,wss

[ios-client](!)
type=friend
context=from-internal
host=dynamic
nat=force_rport,comedia
canreinvite=no
insecure=invite,port
qualify=yes
dtmfmode=rfc2833
disallow=all
allow=ulaw,alaw,gsm,g729

[1001](ios-client)
secret=your-password
callerid="iOS Client" <1001>
```

### 3. WebRTC Server (Alternative)

For WebRTC-based implementation:

```javascript
// Node.js WebRTC signaling server
const WebSocket = require('ws');
const wss = new WebSocket.Server({ port: 8080 });

const clients = new Map();

wss.on('connection', (ws) => {
    ws.on('message', (message) => {
        const data = JSON.parse(message);
        
        switch (data.type) {
            case 'register':
                clients.set(data.deviceId, ws);
                break;
                
            case 'call':
                const targetClient = clients.get(data.targetId);
                if (targetClient) {
                    targetClient.send(JSON.stringify({
                        type: 'incoming-call',
                        callerId: data.callerId,
                        callerName: data.callerName,
                        offer: data.offer
                    }));
                }
                break;
                
            case 'answer':
                const callerClient = clients.get(data.callerId);
                if (callerClient) {
                    callerClient.send(JSON.stringify({
                        type: 'call-answered',
                        answer: data.answer
                    }));
                }
                break;
        }
    });
});
```

## iOS App Integration Points

### 1. Push Token Registration

The iOS app will send its push token to your backend:

```swift
// In VoIPManager.swift
private func sendPushTokenToServer(_ token: String) {
    let url = URL(string: "https://your-backend.com/api/register-push-token")!
    var request = URLRequest(url: url)
    request.httpMethod = "POST"
    request.setValue("application/json", forHTTPHeaderField: "Content-Type")
    
    let payload = [
        "device_id": UIDevice.current.identifierForVendor?.uuidString ?? "",
        "push_token": token,
        "platform": "ios"
    ]
    
    request.httpBody = try? JSONSerialization.data(withJSONObject: payload)
    
    URLSession.shared.dataTask(with: request) { data, response, error in
        if let error = error {
            LoggingManager.shared.log("Failed to register push token: \(error)", level: .error)
        } else {
            LoggingManager.shared.log("Push token registered successfully", level: .info)
        }
    }.resume()
}
```

### 2. SIP Registration

Configure SIP registration in the iOS app:

```swift
// In VoIPManager.swift - implement actual SIP registration
private func registerWithServer() {
    guard let config = configuration else { return }
    
    // Example using a SIP library (pseudo-code)
    let sipAccount = SipAccount(
        username: config.username,
        password: config.password,
        domain: config.domain,
        server: config.serverAddress,
        port: config.serverPort,
        transport: config.transport.rawValue
    )
    
    sipClient.register(account: sipAccount) { [weak self] result in
        switch result {
        case .success:
            self?.isRegistered = true
            LoggingManager.shared.log("SIP registration successful", level: .info)
        case .failure(let error):
            LoggingManager.shared.log("SIP registration failed: \(error)", level: .error)
            self?.delegate?.voipManager(self!, didEncounterError: .sipRegistrationFailed(error.localizedDescription))
        }
    }
}
```

## Call Flow Implementation

### 1. Incoming Call Handling

```python
# Backend: Handle incoming call from Android
def handle_incoming_call(android_device_id, caller_id, caller_name=None):
    # Find associated iOS device
    ios_device = get_paired_ios_device(android_device_id)
    if not ios_device:
        return False
    
    # Send VoIP push notification
    push_sent = send_voip_push(
        device_token=ios_device.push_token,
        caller_id=caller_id,
        caller_name=caller_name
    )
    
    if push_sent:
        # Setup SIP call to iOS device
        setup_sip_call(ios_device.sip_username, caller_id, caller_name)
        return True
    
    return False

def setup_sip_call(ios_username, caller_id, caller_name):
    # Use your SIP server API to initiate call
    sip_server.initiate_call(
        from_number=caller_id,
        to_username=ios_username,
        caller_name=caller_name
    )
```

### 2. Call Bridging

```python
# Bridge audio between Android and iOS
def bridge_call(android_session, ios_session):
    # Setup RTP forwarding between sessions
    rtp_bridge = RTPBridge()
    rtp_bridge.connect(android_session.rtp_endpoint, ios_session.rtp_endpoint)
    
    # Monitor call quality
    quality_monitor = CallQualityMonitor()
    quality_monitor.monitor_sessions([android_session, ios_session])
    
    return rtp_bridge
```

## Database Schema

Store device associations and call logs:

```sql
-- Device registration table
CREATE TABLE devices (
    id UUID PRIMARY KEY,
    device_type VARCHAR(10) NOT NULL, -- 'android' or 'ios'
    device_id VARCHAR(255) UNIQUE NOT NULL,
    push_token VARCHAR(255),
    sip_username VARCHAR(100),
    sip_password VARCHAR(100),
    paired_device_id UUID REFERENCES devices(id),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Call logs table
CREATE TABLE call_logs (
    id UUID PRIMARY KEY,
    android_device_id UUID REFERENCES devices(id),
    ios_device_id UUID REFERENCES devices(id),
    caller_id VARCHAR(50),
    caller_name VARCHAR(100),
    call_start TIMESTAMP,
    call_end TIMESTAMP,
    call_duration INTEGER, -- seconds
    call_status VARCHAR(20), -- 'completed', 'missed', 'failed'
    created_at TIMESTAMP DEFAULT NOW()
);
```

## API Endpoints

### Device Management

```python
# Flask example
from flask import Flask, request, jsonify

app = Flask(__name__)

@app.route('/api/register-push-token', methods=['POST'])
def register_push_token():
    data = request.json
    device_id = data.get('device_id')
    push_token = data.get('push_token')
    platform = data.get('platform')
    
    # Update device record
    device = Device.query.filter_by(device_id=device_id).first()
    if device:
        device.push_token = push_token
        device.updated_at = datetime.utcnow()
    else:
        device = Device(
            device_id=device_id,
            device_type=platform,
            push_token=push_token
        )
        db.session.add(device)
    
    db.session.commit()
    return jsonify({'status': 'success'})

@app.route('/api/pair-devices', methods=['POST'])
def pair_devices():
    data = request.json
    android_device_id = data.get('android_device_id')
    ios_device_id = data.get('ios_device_id')
    
    # Create device pairing
    android_device = Device.query.filter_by(device_id=android_device_id).first()
    ios_device = Device.query.filter_by(device_id=ios_device_id).first()
    
    if android_device and ios_device:
        android_device.paired_device_id = ios_device.id
        ios_device.paired_device_id = android_device.id
        db.session.commit()
        return jsonify({'status': 'success'})
    
    return jsonify({'status': 'error', 'message': 'Devices not found'}), 404
```

## Security Considerations

### 1. Authentication
```python
# JWT-based authentication
def authenticate_device(token):
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=['HS256'])
        return payload.get('device_id')
    except jwt.InvalidTokenError:
        return None
```

### 2. Encryption
- Use TLS for all HTTP communications
- Enable SRTP for SIP calls
- Use DTLS for WebRTC connections

### 3. Rate Limiting
```python
from flask_limiter import Limiter

limiter = Limiter(
    app,
    key_func=lambda: request.headers.get('X-Device-ID', 'anonymous'),
    default_limits=["100 per hour"]
)

@app.route('/api/register-push-token', methods=['POST'])
@limiter.limit("10 per minute")
def register_push_token():
    # Implementation
    pass
```

## Testing

### 1. Push Notification Testing
```bash
# Test VoIP push using curl
curl -X POST \
  -H "authorization: bearer $JWT_TOKEN" \
  -H "apns-topic: com.yourapp.voipreceiver.voip" \
  -H "apns-push-type: voip" \
  -H "apns-priority: 10" \
  -d '{"aps":{"alert":"Test call"},"caller_id":"+1234567890"}' \
  https://api.development.push.apple.com/3/device/$DEVICE_TOKEN
```

### 2. SIP Testing
```bash
# Test SIP registration
sipp -sn uac -s 1001 your-sip-server.com:5060
```

## Monitoring and Analytics

### 1. Call Quality Metrics
```python
def log_call_quality(call_id, metrics):
    quality_log = CallQualityLog(
        call_id=call_id,
        jitter=metrics.get('jitter'),
        packet_loss=metrics.get('packet_loss'),
        rtt=metrics.get('rtt'),
        audio_level=metrics.get('audio_level')
    )
    db.session.add(quality_log)
    db.session.commit()
```

### 2. Push Notification Analytics
```python
def track_push_notification(device_token, status, response_time):
    push_log = PushNotificationLog(
        device_token=device_token,
        status=status,
        response_time=response_time,
        timestamp=datetime.utcnow()
    )
    db.session.add(push_log)
    db.session.commit()
```

This backend integration provides a complete foundation for connecting your iOS VoIP app with Android devices through a server-based bridge system.
