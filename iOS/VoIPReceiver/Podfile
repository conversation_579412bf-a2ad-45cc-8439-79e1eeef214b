# Uncomment the next line to define a global platform for your project
platform :ios, '13.0'

target 'VoIPReceiver' do
  # Comment the next line if you don't want to use dynamic frameworks
  use_frameworks!

  # Pods for VoIPReceiver
  
  # VoIP and SIP Libraries
  # Uncomment one of the following based on your preferred VoIP solution:
  
  # Option 1: Linphone SDK (SIP-based)
  # pod 'linphone-sdk', '~> 5.2.0'
  
  # Option 2: PJSIP (Alternative SIP library)
  # pod 'PJSIP', '~> 2.13'
  
  # Option 3: WebRTC (for WebRTC-based calls)
  # pod 'GoogleWebRTC', '~> 1.1.31999'
  
  # Option 4: Custom SIP library (example)
  # pod 'SipStack', :git => 'https://github.com/your-repo/sip-stack.git'
  
  # Network and HTTP libraries
  pod 'Alamofire', '~> 5.8'
  pod 'SwiftyJSON', '~> 5.0'
  
  # Audio processing (optional)
  # pod 'AudioKit', '~> 5.6'
  
  # Logging (optional - for enhanced logging)
  # pod 'CocoaLumberjack/Swift', '~> 3.8'
  
  # Keychain for secure storage
  pod 'KeychainAccess', '~> 4.2'
  
  # Reachability for network monitoring
  pod 'ReachabilitySwift', '~> 5.0'

  target 'VoIPReceiverTests' do
    inherit! :search_paths
    # Pods for testing
    pod 'Quick', '~> 7.0'
    pod 'Nimble', '~> 12.0'
  end

  target 'VoIPReceiverUITests' do
    # Pods for testing
  end

end

# Post-install script to configure build settings
post_install do |installer|
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      # Set minimum deployment target
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '13.0'
      
      # Enable bitcode (if needed)
      config.build_settings['ENABLE_BITCODE'] = 'NO'
      
      # Set valid architectures
      config.build_settings['VALID_ARCHS'] = 'arm64 x86_64'
      
      # Exclude arm64 for simulator (if needed for older dependencies)
      config.build_settings['EXCLUDED_ARCHS[sdk=iphonesimulator*]'] = 'arm64'
      
      # Set Swift version
      if config.build_settings['SWIFT_VERSION'].nil?
        config.build_settings['SWIFT_VERSION'] = '5.0'
      end
    end
  end
end
