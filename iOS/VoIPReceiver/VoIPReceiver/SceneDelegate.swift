import UIKit

class SceneDelegate: UIResponder, UIWindowSceneDelegate {

    var window: UIWindow?

    func scene(_ scene: UIScene, willConnectTo session: UISceneSession, options connectionOptions: UIScene.ConnectionOptions) {
        guard let windowScene = (scene as? UIWindowScene) else { return }
        
        window = UIWindow(windowScene: windowScene)
        
        // Create main view controller
        let storyboard = UIStoryboard(name: "Main", bundle: nil)
        let mainViewController = storyboard.instantiateViewController(withIdentifier: "ViewController") as! ViewController
        
        // Wrap in navigation controller
        let navigationController = UINavigationController(rootViewController: mainViewController)
        
        window?.rootViewController = navigationController
        window?.makeKeyAndVisible()
        
        LoggingManager.shared.log("Scene connected", level: .info)
    }

    func sceneDidDisconnect(_ scene: UIScene) {
        LoggingManager.shared.log("Scene disconnected", level: .info)
    }

    func sceneDidBecomeActive(_ scene: UIScene) {
        LoggingManager.shared.log("Scene became active", level: .info)
    }

    func sceneWillResignActive(_ scene: UIScene) {
        LoggingManager.shared.log("Scene will resign active", level: .info)
    }

    func sceneWillEnterForeground(_ scene: UIScene) {
        LoggingManager.shared.log("Scene will enter foreground", level: .info)
    }

    func sceneDidEnterBackground(_ scene: UIScene) {
        LoggingManager.shared.log("Scene entered background", level: .info)
    }
}
