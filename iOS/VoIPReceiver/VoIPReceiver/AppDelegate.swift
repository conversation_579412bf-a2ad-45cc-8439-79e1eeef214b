import UIKit
import Push<PERSON>it
import Call<PERSON>it

@main
class AppDelegate: UIResponder, UIApplicationDelegate {
    
    var window: UIWindow?
    
    // Managers
    lazy var voipManager = VoIPManager.shared
    lazy var pushKitManager = PushKitManager.shared
    lazy var callKitManager = CallKitManager.shared
    lazy var loggingManager = LoggingManager.shared
    
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        
        // Initialize logging
        loggingManager.log("App launched", level: .info)
        
        // Setup VoIP managers
        setupVoIPManagers()
        
        // Request permissions
        requestPermissions()
        
        return true
    }
    
    private func setupVoIPManagers() {
        // Initialize PushKit for VoIP notifications
        pushKitManager.delegate = self
        pushKitManager.registerForVoIPPushes()
        
        // Initialize CallKit
        callKitManager.delegate = self
        
        // Initialize VoIP manager
        voipManager.delegate = self
        voipManager.initialize()
        
        loggingManager.log("VoIP managers initialized", level: .info)
    }
    
    private func requestPermissions() {
        // Request microphone permission
        AVAudioSession.sharedInstance().requestRecordPermission { granted in
            DispatchQueue.main.async {
                if granted {
                    self.loggingManager.log("Microphone permission granted", level: .info)
                } else {
                    self.loggingManager.log("Microphone permission denied", level: .error)
                }
            }
        }
    }
    
    // MARK: - App Lifecycle
    
    func applicationDidEnterBackground(_ application: UIApplication) {
        loggingManager.log("App entered background", level: .info)
        voipManager.handleAppDidEnterBackground()
    }
    
    func applicationWillEnterForeground(_ application: UIApplication) {
        loggingManager.log("App will enter foreground", level: .info)
        voipManager.handleAppWillEnterForeground()
    }
    
    func applicationDidBecomeActive(_ application: UIApplication) {
        loggingManager.log("App became active", level: .info)
    }
    
    func applicationWillTerminate(_ application: UIApplication) {
        loggingManager.log("App will terminate", level: .info)
        voipManager.cleanup()
    }
}

// MARK: - PushKit Delegate
extension AppDelegate: PushKitManagerDelegate {
    func pushKitManager(_ manager: PushKitManager, didReceiveIncomingCall callInfo: [String: Any]) {
        loggingManager.log("Received VoIP push notification: \(callInfo)", level: .info)
        
        // Extract call information
        let callerID = callInfo["caller_id"] as? String ?? "Unknown"
        let callUUID = UUID()
        
        // Report incoming call to CallKit
        callKitManager.reportIncomingCall(uuid: callUUID, handle: callerID) { error in
            if let error = error {
                self.loggingManager.log("Failed to report incoming call: \(error)", level: .error)
            } else {
                self.loggingManager.log("Successfully reported incoming call", level: .info)
            }
        }
    }
    
    func pushKitManager(_ manager: PushKitManager, didUpdateCredentials credentials: PKPushCredentials) {
        let token = credentials.token.map { String(format: "%02.2hhx", $0) }.joined()
        loggingManager.log("VoIP push token updated: \(token)", level: .info)
        
        // Send token to your backend server
        voipManager.updatePushToken(token)
    }
}

// MARK: - CallKit Manager Delegate
extension AppDelegate: CallKitManagerDelegate {
    func callKitManager(_ manager: CallKitManager, didAnswerCall uuid: UUID) {
        loggingManager.log("User answered call: \(uuid)", level: .info)
        voipManager.answerCall(uuid: uuid)
    }
    
    func callKitManager(_ manager: CallKitManager, didEndCall uuid: UUID) {
        loggingManager.log("Call ended: \(uuid)", level: .info)
        voipManager.endCall(uuid: uuid)
    }
    
    func callKitManager(_ manager: CallKitManager, didHoldCall uuid: UUID, onHold: Bool) {
        loggingManager.log("Call \(uuid) hold status: \(onHold)", level: .info)
        voipManager.holdCall(uuid: uuid, onHold: onHold)
    }
}

// MARK: - VoIP Manager Delegate
extension AppDelegate: VoIPManagerDelegate {
    func voipManager(_ manager: VoIPManager, didReceiveIncomingCall callInfo: CallSession) {
        loggingManager.log("VoIP manager received incoming call: \(callInfo.callerID)", level: .info)
        
        // This is handled by PushKit, but can be used for direct SIP calls
        callKitManager.reportIncomingCall(uuid: callInfo.uuid, handle: callInfo.callerID) { error in
            if let error = error {
                self.loggingManager.log("Failed to report direct incoming call: \(error)", level: .error)
            }
        }
    }
    
    func voipManager(_ manager: VoIPManager, didUpdateCallState state: CallState, for uuid: UUID) {
        loggingManager.log("Call \(uuid) state updated to: \(state)", level: .info)
        
        switch state {
        case .connected:
            callKitManager.updateCall(uuid: uuid, connected: true)
        case .ended:
            callKitManager.endCall(uuid: uuid)
        case .failed:
            callKitManager.endCall(uuid: uuid)
        default:
            break
        }
    }
}
