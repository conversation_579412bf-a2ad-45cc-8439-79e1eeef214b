import Foundation

// MARK: - Call State Enum
enum CallState: String, CaseIterable {
    case idle = "idle"
    case incoming = "incoming"
    case outgoing = "outgoing"
    case connecting = "connecting"
    case connected = "connected"
    case onHold = "on_hold"
    case ended = "ended"
    case failed = "failed"
    
    var displayName: String {
        switch self {
        case .idle: return "Idle"
        case .incoming: return "Incoming"
        case .outgoing: return "Outgoing"
        case .connecting: return "Connecting"
        case .connected: return "Connected"
        case .onHold: return "On Hold"
        case .ended: return "Ended"
        case .failed: return "Failed"
        }
    }
}

// MARK: - Call Session Model
class CallSession: NSObject {
    let uuid: UUID
    let callerID: String
    let callerName: String?
    let startTime: Date
    var endTime: Date?
    var state: CallState
    var duration: TimeInterval {
        if let endTime = endTime {
            return endTime.timeIntervalSince(startTime)
        } else {
            return Date().timeIntervalSince(startTime)
        }
    }
    
    // SIP/WebRTC specific properties
    var sipCallID: String?
    var remoteAddress: String?
    var localAddress: String?
    var codec: String?
    var isVideoCall: Bool
    
    // Call quality metrics
    var audioQuality: CallQuality?
    var networkStats: NetworkStats?
    
    init(uuid: UUID = UUID(),
         callerID: String,
         callerName: String? = nil,
         isVideoCall: Bool = false) {
        self.uuid = uuid
        self.callerID = callerID
        self.callerName = callerName
        self.startTime = Date()
        self.state = .idle
        self.isVideoCall = isVideoCall
        super.init()
    }
    
    func updateState(_ newState: CallState) {
        let oldState = self.state
        self.state = newState
        
        if newState == .ended || newState == .failed {
            self.endTime = Date()
        }
        
        LoggingManager.shared.log("Call \(uuid) state changed from \(oldState.displayName) to \(newState.displayName)", level: .info)
    }
    
    func formattedDuration() -> String {
        let duration = self.duration
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
}

// MARK: - Call Quality Model
struct CallQuality {
    let jitter: Double // in milliseconds
    let packetLoss: Double // percentage
    let roundTripTime: Double // in milliseconds
    let audioLevel: Double // dB
    let timestamp: Date
    
    var qualityScore: CallQualityScore {
        // Simple quality scoring based on network metrics
        if packetLoss > 5.0 || jitter > 150 || roundTripTime > 300 {
            return .poor
        } else if packetLoss > 2.0 || jitter > 75 || roundTripTime > 150 {
            return .fair
        } else {
            return .good
        }
    }
}

enum CallQualityScore: String {
    case good = "good"
    case fair = "fair"
    case poor = "poor"
    
    var displayName: String {
        switch self {
        case .good: return "Good"
        case .fair: return "Fair"
        case .poor: return "Poor"
        }
    }
}

// MARK: - Network Stats Model
struct NetworkStats {
    let bytesReceived: UInt64
    let bytesSent: UInt64
    let packetsReceived: UInt64
    let packetsSent: UInt64
    let packetsLost: UInt64
    let timestamp: Date
    
    var packetLossPercentage: Double {
        let totalPackets = packetsReceived + packetsLost
        return totalPackets > 0 ? (Double(packetsLost) / Double(totalPackets)) * 100.0 : 0.0
    }
}

// MARK: - Call Direction Enum
enum CallDirection: String {
    case incoming = "incoming"
    case outgoing = "outgoing"
    
    var displayName: String {
        switch self {
        case .incoming: return "Incoming"
        case .outgoing: return "Outgoing"
        }
    }
}
