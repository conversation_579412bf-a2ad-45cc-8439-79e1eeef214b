<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES" initialViewController="BYZ-38-t0r">
    <device id="retina6_12" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21679"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <scenes>
        <!--View Controller-->
        <scene sceneID="tne-QT-ifu">
            <objects>
                <viewController storyboardIdentifier="ViewController" id="BYZ-38-t0r" customClass="ViewController" customModule="VoIPReceiver" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="8bC-Xf-vdC">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Qhg-hd-8Nh">
                                <rect key="frame" x="0.0" y="59" width="393" height="759"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="vUN-kp-3ea">
                                        <rect key="frame" x="0.0" y="0.0" width="393" height="800"/>
                                        <subviews>
                                            <!-- Status Section -->
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="status-view">
                                                <rect key="frame" x="20" y="20" width="353" height="120"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="VoIP Service Status" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="status-title">
                                                        <rect key="frame" x="16" y="16" width="321" height="21"/>
                                                        <fontDescription key="fontDescription" type="boldSystem" pointSize="17"/>
                                                        <color key="textColor" systemColor="labelColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="VoIP Service Inactive" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="status-label">
                                                        <rect key="frame" x="16" y="45" width="321" height="21"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                        <color key="textColor" systemColor="systemRedColor"/>
                                                    </label>
                                                    <switch opaque="NO" contentMode="scaleToFill" horizontalHuggingPriority="750" verticalHuggingPriority="750" contentHorizontalAlignment="center" contentVerticalAlignment="center" translatesAutoresizingMaskIntoConstraints="NO" id="availability-switch">
                                                        <rect key="frame" x="162" y="74" width="51" height="31"/>
                                                    </switch>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="connection-status">
                                                        <rect key="frame" x="16" y="16" width="16" height="16"/>
                                                        <color key="backgroundColor" systemColor="systemRedColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="16" id="connection-width"/>
                                                            <constraint firstAttribute="height" constant="16" id="connection-height"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                                <color key="backgroundColor" systemColor="systemGray6Color"/>
                                                <constraints>
                                                    <constraint firstItem="status-title" firstAttribute="leading" secondItem="status-view" secondAttribute="leading" constant="16" id="title-leading"/>
                                                    <constraint firstItem="status-title" firstAttribute="top" secondItem="status-view" secondAttribute="top" constant="16" id="title-top"/>
                                                    <constraint firstAttribute="trailing" secondItem="status-title" secondAttribute="trailing" constant="16" id="title-trailing"/>
                                                    <constraint firstItem="status-label" firstAttribute="leading" secondItem="status-title" secondAttribute="leading" id="label-leading"/>
                                                    <constraint firstItem="status-label" firstAttribute="top" secondItem="status-title" secondAttribute="bottom" constant="8" id="label-top"/>
                                                    <constraint firstItem="status-label" firstAttribute="trailing" secondItem="status-title" secondAttribute="trailing" id="label-trailing"/>
                                                    <constraint firstItem="availability-switch" firstAttribute="centerX" secondItem="status-view" secondAttribute="centerX" id="switch-centerx"/>
                                                    <constraint firstItem="availability-switch" firstAttribute="top" secondItem="status-label" secondAttribute="bottom" constant="8" id="switch-top"/>
                                                    <constraint firstAttribute="bottom" secondItem="availability-switch" secondAttribute="bottom" constant="15" id="switch-bottom"/>
                                                    <constraint firstItem="connection-status" firstAttribute="leading" secondItem="status-view" secondAttribute="leading" constant="16" id="connection-leading"/>
                                                    <constraint firstItem="connection-status" firstAttribute="top" secondItem="status-view" secondAttribute="top" constant="16" id="connection-top"/>
                                                    <constraint firstAttribute="height" constant="120" id="status-height"/>
                                                </constraints>
                                            </view>
                                            
                                            <!-- Call Info Section -->
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="call-info-view">
                                                <rect key="frame" x="20" y="160" width="353" height="150"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Current Call" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="call-title">
                                                        <rect key="frame" x="16" y="16" width="321" height="21"/>
                                                        <fontDescription key="fontDescription" type="boldSystem" pointSize="17"/>
                                                        <color key="textColor" systemColor="labelColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="No active call" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="current-call-label">
                                                        <rect key="frame" x="16" y="45" width="321" height="21"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                        <color key="textColor" systemColor="labelColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="caller-id-label">
                                                        <rect key="frame" x="16" y="74" width="321" height="21"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="15"/>
                                                        <color key="textColor" systemColor="systemGrayColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="00:00" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="call-duration-label">
                                                        <rect key="frame" x="16" y="103" width="321" height="21"/>
                                                        <fontDescription key="fontDescription" type="monospacedDigitSystem" pointSize="20"/>
                                                        <color key="textColor" systemColor="labelColor"/>
                                                    </label>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="quality-indicator">
                                                        <rect key="frame" x="321" y="16" width="16" height="16"/>
                                                        <color key="backgroundColor" systemColor="systemGrayColor"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="16" id="quality-width"/>
                                                            <constraint firstAttribute="height" constant="16" id="quality-height"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                                <color key="backgroundColor" systemColor="systemGray6Color"/>
                                                <constraints>
                                                    <constraint firstItem="call-title" firstAttribute="leading" secondItem="call-info-view" secondAttribute="leading" constant="16" id="call-title-leading"/>
                                                    <constraint firstItem="call-title" firstAttribute="top" secondItem="call-info-view" secondAttribute="top" constant="16" id="call-title-top"/>
                                                    <constraint firstAttribute="trailing" secondItem="call-title" secondAttribute="trailing" constant="16" id="call-title-trailing"/>
                                                    <constraint firstItem="current-call-label" firstAttribute="leading" secondItem="call-title" secondAttribute="leading" id="current-call-leading"/>
                                                    <constraint firstItem="current-call-label" firstAttribute="top" secondItem="call-title" secondAttribute="bottom" constant="8" id="current-call-top"/>
                                                    <constraint firstItem="current-call-label" firstAttribute="trailing" secondItem="call-title" secondAttribute="trailing" id="current-call-trailing"/>
                                                    <constraint firstItem="caller-id-label" firstAttribute="leading" secondItem="current-call-label" secondAttribute="leading" id="caller-id-leading"/>
                                                    <constraint firstItem="caller-id-label" firstAttribute="top" secondItem="current-call-label" secondAttribute="bottom" constant="8" id="caller-id-top"/>
                                                    <constraint firstItem="caller-id-label" firstAttribute="trailing" secondItem="current-call-label" secondAttribute="trailing" id="caller-id-trailing"/>
                                                    <constraint firstItem="call-duration-label" firstAttribute="leading" secondItem="caller-id-label" secondAttribute="leading" id="duration-leading"/>
                                                    <constraint firstItem="call-duration-label" firstAttribute="top" secondItem="caller-id-label" secondAttribute="bottom" constant="8" id="duration-top"/>
                                                    <constraint firstItem="call-duration-label" firstAttribute="trailing" secondItem="caller-id-label" secondAttribute="trailing" id="duration-trailing"/>
                                                    <constraint firstAttribute="bottom" secondItem="call-duration-label" secondAttribute="bottom" constant="26" id="duration-bottom"/>
                                                    <constraint firstItem="quality-indicator" firstAttribute="trailing" secondItem="call-info-view" secondAttribute="trailing" constant="-16" id="quality-trailing"/>
                                                    <constraint firstItem="quality-indicator" firstAttribute="top" secondItem="call-info-view" secondAttribute="top" constant="16" id="quality-top"/>
                                                    <constraint firstAttribute="height" constant="150" id="call-info-height"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                    </view>
                                </subviews>
                            </scrollView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="6Tk-OE-BBY"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="Qhg-hd-8Nh" firstAttribute="top" secondItem="6Tk-OE-BBY" secondAttribute="top" id="scroll-top"/>
                            <constraint firstItem="Qhg-hd-8Nh" firstAttribute="leading" secondItem="6Tk-OE-BBY" secondAttribute="leading" id="scroll-leading"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="trailing" secondItem="Qhg-hd-8Nh" secondAttribute="trailing" id="scroll-trailing"/>
                            <constraint firstItem="6Tk-OE-BBY" firstAttribute="bottom" secondItem="Qhg-hd-8Nh" secondAttribute="bottom" id="scroll-bottom"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="statusLabel" destination="status-label" id="status-outlet"/>
                        <outlet property="availabilitySwitch" destination="availability-switch" id="switch-outlet"/>
                        <outlet property="currentCallLabel" destination="current-call-label" id="current-call-outlet"/>
                        <outlet property="callDurationLabel" destination="call-duration-label" id="duration-outlet"/>
                        <outlet property="callerIDLabel" destination="caller-id-label" id="caller-id-outlet"/>
                        <outlet property="connectionStatusView" destination="connection-status" id="connection-outlet"/>
                        <outlet property="qualityIndicatorView" destination="quality-indicator" id="quality-outlet"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="dkx-z0-nzr" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="20" y="4"/>
        </scene>
        
        <!--Call History View Controller-->
        <scene sceneID="call-history-scene">
            <objects>
                <viewController storyboardIdentifier="CallHistoryViewController" id="call-history-vc" customClass="CallHistoryViewController" customModule="VoIPReceiver" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="call-history-view">
                        <rect key="frame" x="0.0" y="0.0" width="393" height="852"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="statistics-view">
                                <rect key="frame" x="20" y="79" width="353" height="100"/>
                                <color key="backgroundColor" systemColor="systemGray6Color"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="100" id="stats-height"/>
                                </constraints>
                            </view>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="history-table">
                                <rect key="frame" x="0.0" y="199" width="393" height="553"/>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                            </tableView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="clear-history-button">
                                <rect key="frame" x="20" y="772" width="353" height="46"/>
                                <color key="backgroundColor" systemColor="systemRedColor"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="46" id="clear-height"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="boldSystem" pointSize="17"/>
                                <inset key="titleEdgeInsets" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                                <state key="normal" title="Clear History">
                                    <color key="titleColor" systemColor="whiteColor"/>
                                </state>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="call-history-safe-area"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="statistics-view" firstAttribute="top" secondItem="call-history-safe-area" secondAttribute="top" constant="20" id="stats-top"/>
                            <constraint firstItem="statistics-view" firstAttribute="leading" secondItem="call-history-safe-area" secondAttribute="leading" constant="20" id="stats-leading"/>
                            <constraint firstItem="call-history-safe-area" firstAttribute="trailing" secondItem="statistics-view" secondAttribute="trailing" constant="20" id="stats-trailing"/>
                            <constraint firstItem="history-table" firstAttribute="top" secondItem="statistics-view" secondAttribute="bottom" constant="20" id="table-top"/>
                            <constraint firstItem="history-table" firstAttribute="leading" secondItem="call-history-safe-area" secondAttribute="leading" id="table-leading"/>
                            <constraint firstItem="call-history-safe-area" firstAttribute="trailing" secondItem="history-table" secondAttribute="trailing" id="table-trailing"/>
                            <constraint firstItem="clear-history-button" firstAttribute="top" secondItem="history-table" secondAttribute="bottom" constant="20" id="clear-top"/>
                            <constraint firstItem="clear-history-button" firstAttribute="leading" secondItem="call-history-safe-area" secondAttribute="leading" constant="20" id="clear-leading"/>
                            <constraint firstItem="call-history-safe-area" firstAttribute="trailing" secondItem="clear-history-button" secondAttribute="trailing" constant="20" id="clear-trailing"/>
                            <constraint firstItem="call-history-safe-area" firstAttribute="bottom" secondItem="clear-history-button" secondAttribute="bottom" id="clear-bottom"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="tableView" destination="history-table" id="table-outlet"/>
                        <outlet property="statisticsView" destination="statistics-view" id="stats-outlet"/>
                        <outlet property="clearHistoryButton" destination="clear-history-button" id="clear-outlet"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="call-history-responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="750" y="4"/>
        </scene>
    </scenes>
    <resources>
        <systemColor name="labelColor">
            <color white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
        <systemColor name="systemGray6Color">
            <color red="0.94901960784313721" green="0.94901960784313721" blue="0.96862745098039216" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
        <systemColor name="systemGrayColor">
            <color red="0.55686274509803924" green="0.55686274509803924" blue="0.57647058823529407" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
        <systemColor name="systemRedColor">
            <color red="1" green="0.23137254901960785" blue="0.18823529411764706" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
        </systemColor>
        <systemColor name="whiteColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>
