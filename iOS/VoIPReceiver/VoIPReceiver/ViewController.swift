import UIKit
import CallKit

class ViewController: UIViewController {
    
    // MARK: - IBOutlets
    @IBOutlet weak var statusLabel: UILabel!
    @IBOutlet weak var availabilitySwitch: UISwitch!
    @IBOutlet weak var currentCallLabel: UILabel!
    @IBOutlet weak var callDurationLabel: UILabel!
    @IBOutlet weak var callerIDLabel: UILabel!
    @IBOutlet weak var audioRouteButton: UIButton!
    @IBOutlet weak var muteButton: UIButton!
    @IBOutlet weak var speakerButton: UIButton!
    @IBOutlet weak var endCallButton: UIButton!
    @IBOutlet weak var callHistoryButton: UIButton!
    @IBOutlet weak var logsButton: UIButton!
    @IBOutlet weak var connectionStatusView: UIView!
    @IBOutlet weak var qualityIndicatorView: UIView!
    
    // MARK: - Properties
    private let voipManager = VoIPManager.shared
    private let audioManager = AudioManager.shared
    private let loggingManager = LoggingManager.shared
    private let callHistoryManager = CallHistoryManager.shared
    
    private var currentCall: CallSession?
    private var callTimer: Timer?
    private var isCallAvailable = false
    private var isMuted = false
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupDelegates()
        updateUI()
        
        loggingManager.log("Main view controller loaded", level: .info, category: .ui)
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        updateConnectionStatus()
        updateCallStatus()
    }
    
    // MARK: - Setup Methods
    
    private func setupUI() {
        title = "VoIP Receiver"
        
        // Configure status label
        statusLabel.text = "VoIP Service Inactive"
        statusLabel.textColor = .systemRed
        
        // Configure availability switch
        availabilitySwitch.isOn = false
        availabilitySwitch.addTarget(self, action: #selector(availabilitySwitchChanged), for: .valueChanged)
        
        // Configure call info labels
        currentCallLabel.text = "No active call"
        callDurationLabel.text = "00:00"
        callerIDLabel.text = ""
        
        // Configure buttons
        setupButtons()
        
        // Configure status views
        connectionStatusView.layer.cornerRadius = 8
        connectionStatusView.backgroundColor = .systemRed
        
        qualityIndicatorView.layer.cornerRadius = 8
        qualityIndicatorView.backgroundColor = .systemGray
        
        // Hide call controls initially
        hideCallControls()
    }
    
    private func setupButtons() {
        audioRouteButton.setTitle("iPhone", for: .normal)
        audioRouteButton.addTarget(self, action: #selector(audioRouteButtonTapped), for: .touchUpInside)
        
        muteButton.setTitle("Mute", for: .normal)
        muteButton.addTarget(self, action: #selector(muteButtonTapped), for: .touchUpInside)
        
        speakerButton.setTitle("Speaker", for: .normal)
        speakerButton.addTarget(self, action: #selector(speakerButtonTapped), for: .touchUpInside)
        
        endCallButton.setTitle("End Call", for: .normal)
        endCallButton.backgroundColor = .systemRed
        endCallButton.setTitleColor(.white, for: .normal)
        endCallButton.layer.cornerRadius = 8
        endCallButton.addTarget(self, action: #selector(endCallButtonTapped), for: .touchUpInside)
        
        callHistoryButton.setTitle("Call History", for: .normal)
        callHistoryButton.addTarget(self, action: #selector(callHistoryButtonTapped), for: .touchUpInside)
        
        logsButton.setTitle("View Logs", for: .normal)
        logsButton.addTarget(self, action: #selector(logsButtonTapped), for: .touchUpInside)
    }
    
    private func setupDelegates() {
        voipManager.delegate = self
        audioManager.delegate = self
    }
    
    // MARK: - UI Updates
    
    private func updateUI() {
        DispatchQueue.main.async { [weak self] in
            self?.updateConnectionStatus()
            self?.updateCallStatus()
            self?.updateAudioRouteButton()
        }
    }
    
    private func updateConnectionStatus() {
        if isCallAvailable {
            statusLabel.text = "VoIP Service Active"
            statusLabel.textColor = .systemGreen
            connectionStatusView.backgroundColor = .systemGreen
        } else {
            statusLabel.text = "VoIP Service Inactive"
            statusLabel.textColor = .systemRed
            connectionStatusView.backgroundColor = .systemRed
        }
    }
    
    private func updateCallStatus() {
        if let call = currentCall {
            currentCallLabel.text = "Call with \(call.callerName ?? call.callerID)"
            callerIDLabel.text = call.callerID
            
            switch call.state {
            case .incoming:
                currentCallLabel.text = "Incoming call from \(call.callerName ?? call.callerID)"
            case .connecting:
                currentCallLabel.text = "Connecting..."
            case .connected:
                currentCallLabel.text = "Connected to \(call.callerName ?? call.callerID)"
                showCallControls()
            case .onHold:
                currentCallLabel.text = "On hold with \(call.callerName ?? call.callerID)"
            case .ended, .failed:
                currentCallLabel.text = "Call ended"
                hideCallControls()
            default:
                break
            }
        } else {
            currentCallLabel.text = "No active call"
            callerIDLabel.text = ""
            callDurationLabel.text = "00:00"
            hideCallControls()
        }
    }
    
    private func updateAudioRouteButton() {
        let currentRoute = audioManager.getCurrentRoute()
        audioRouteButton.setTitle(currentRoute.displayName, for: .normal)
    }
    
    private func showCallControls() {
        audioRouteButton.isHidden = false
        muteButton.isHidden = false
        speakerButton.isHidden = false
        endCallButton.isHidden = false
        
        startCallTimer()
    }
    
    private func hideCallControls() {
        audioRouteButton.isHidden = true
        muteButton.isHidden = true
        speakerButton.isHidden = true
        endCallButton.isHidden = true
        
        stopCallTimer()
    }
    
    private func startCallTimer() {
        stopCallTimer()
        
        callTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            self?.updateCallDuration()
        }
    }
    
    private func stopCallTimer() {
        callTimer?.invalidate()
        callTimer = nil
    }
    
    private func updateCallDuration() {
        guard let call = currentCall else { return }
        
        DispatchQueue.main.async { [weak self] in
            self?.callDurationLabel.text = call.formattedDuration()
        }
    }
    
    private func updateCallQuality(_ quality: CallQuality) {
        DispatchQueue.main.async { [weak self] in
            switch quality.qualityScore {
            case .good:
                self?.qualityIndicatorView.backgroundColor = .systemGreen
            case .fair:
                self?.qualityIndicatorView.backgroundColor = .systemYellow
            case .poor:
                self?.qualityIndicatorView.backgroundColor = .systemRed
            }
        }
    }

    // MARK: - Button Actions

    @objc private func availabilitySwitchChanged() {
        isCallAvailable = availabilitySwitch.isOn

        if isCallAvailable {
            loggingManager.log("VoIP service enabled by user", level: .info, category: .ui)
            // TODO: Enable VoIP service
        } else {
            loggingManager.log("VoIP service disabled by user", level: .info, category: .ui)
            // TODO: Disable VoIP service
        }

        updateConnectionStatus()
    }

    @objc private func audioRouteButtonTapped() {
        loggingManager.log("Audio route button tapped", level: .info, category: .ui)

        let availableRoutes = audioManager.getAvailableRoutes()

        let alertController = UIAlertController(title: "Audio Route", message: "Select audio output", preferredStyle: .actionSheet)

        for route in availableRoutes {
            let action = UIAlertAction(title: route.displayName, style: .default) { [weak self] _ in
                self?.audioManager.setAudioRoute(route)
            }
            alertController.addAction(action)
        }

        alertController.addAction(UIAlertAction(title: "Cancel", style: .cancel))

        // For iPad
        if let popover = alertController.popoverPresentationController {
            popover.sourceView = audioRouteButton
            popover.sourceRect = audioRouteButton.bounds
        }

        present(alertController, animated: true)
    }

    @objc private func muteButtonTapped() {
        isMuted.toggle()

        muteButton.setTitle(isMuted ? "Unmute" : "Mute", for: .normal)
        muteButton.backgroundColor = isMuted ? .systemRed : .systemBlue

        loggingManager.log("Call mute toggled: \(isMuted)", level: .info, category: .ui)

        // TODO: Implement actual mute functionality
        if let call = currentCall {
            // voipManager.muteCall(uuid: call.uuid, muted: isMuted)
        }
    }

    @objc private func speakerButtonTapped() {
        audioManager.toggleSpeaker()
        loggingManager.log("Speaker toggled", level: .info, category: .ui)
    }

    @objc private func endCallButtonTapped() {
        guard let call = currentCall else { return }

        loggingManager.log("End call button tapped", level: .info, category: .ui)
        voipManager.endCall(uuid: call.uuid)
    }

    @objc private func callHistoryButtonTapped() {
        loggingManager.log("Call history button tapped", level: .info, category: .ui)

        let storyboard = UIStoryboard(name: "Main", bundle: nil)
        if let callHistoryVC = storyboard.instantiateViewController(withIdentifier: "CallHistoryViewController") as? CallHistoryViewController {
            navigationController?.pushViewController(callHistoryVC, animated: true)
        }
    }

    @objc private func logsButtonTapped() {
        loggingManager.log("Logs button tapped", level: .info, category: .ui)

        let logs = loggingManager.getRecentLogs(count: 50)
        let logsText = logs.map { $0.formattedMessage }.joined(separator: "\n")

        let alertController = UIAlertController(title: "Recent Logs", message: logsText, preferredStyle: .alert)
        alertController.addAction(UIAlertAction(title: "Export", style: .default) { [weak self] _ in
            self?.exportLogs()
        })
        alertController.addAction(UIAlertAction(title: "Close", style: .cancel))

        present(alertController, animated: true)
    }

    private func exportLogs() {
        let allLogs = loggingManager.exportLogs()

        let activityViewController = UIActivityViewController(activityItems: [allLogs], applicationActivities: nil)

        // For iPad
        if let popover = activityViewController.popoverPresentationController {
            popover.sourceView = logsButton
            popover.sourceRect = logsButton.bounds
        }

        present(activityViewController, animated: true)
    }
}

// MARK: - VoIP Manager Delegate
extension ViewController: VoIPManagerDelegate {
    func voipManager(_ manager: VoIPManager, didReceiveIncomingCall callInfo: CallSession) {
        loggingManager.log("Received incoming call in UI: \(callInfo.callerID)", level: .info, category: .ui)

        DispatchQueue.main.async { [weak self] in
            self?.currentCall = callInfo
            self?.updateUI()
        }
    }

    func voipManager(_ manager: VoIPManager, didUpdateCallState state: CallState, for uuid: UUID) {
        loggingManager.log("Call state updated in UI: \(state.displayName)", level: .info, category: .ui)

        DispatchQueue.main.async { [weak self] in
            if self?.currentCall?.uuid == uuid {
                self?.currentCall?.updateState(state)
                self?.updateUI()

                if state == .ended || state == .failed {
                    self?.currentCall = nil
                }
            }
        }
    }

    func voipManager(_ manager: VoIPManager, didUpdateAudioQuality quality: CallQuality, for uuid: UUID) {
        if currentCall?.uuid == uuid {
            updateCallQuality(quality)
        }
    }

    func voipManager(_ manager: VoIPManager, didEncounterError error: VoIPError) {
        loggingManager.log("VoIP error in UI: \(error.localizedDescription)", level: .error, category: .ui)

        DispatchQueue.main.async { [weak self] in
            let alertController = UIAlertController(title: "VoIP Error", message: error.localizedDescription, preferredStyle: .alert)
            alertController.addAction(UIAlertAction(title: "OK", style: .default))
            self?.present(alertController, animated: true)
        }
    }
}

// MARK: - Audio Manager Delegate
extension ViewController: AudioManagerDelegate {
    func audioManager(_ manager: AudioManager, didChangeRoute route: AudioRoute) {
        loggingManager.log("Audio route changed in UI: \(route.displayName)", level: .info, category: .ui)

        DispatchQueue.main.async { [weak self] in
            self?.updateAudioRouteButton()
        }
    }

    func audioManager(_ manager: AudioManager, didEncounterError error: AudioError) {
        loggingManager.log("Audio error in UI: \(error.localizedDescription)", level: .error, category: .ui)
    }

    func audioManager(_ manager: AudioManager, didUpdateVolume volume: Float) {
        loggingManager.log("Volume updated in UI: \(volume)", level: .debug, category: .ui)
    }
}
