import Foundation
import os.log

// MARK: - Log Level Enum
enum LogLevel: String, CaseIterable {
    case debug = "DEBUG"
    case info = "INFO"
    case warning = "WARNING"
    case error = "ERROR"
    case critical = "CRITICAL"
    
    var osLogType: OSLogType {
        switch self {
        case .debug: return .debug
        case .info: return .info
        case .warning: return .default
        case .error: return .error
        case .critical: return .fault
        }
    }
    
    var emoji: String {
        switch self {
        case .debug: return "🔍"
        case .info: return "ℹ️"
        case .warning: return "⚠️"
        case .error: return "❌"
        case .critical: return "🚨"
        }
    }
}

// MARK: - Log Category Enum
enum LogCategory: String, CaseIterable {
    case voip = "VoIP"
    case pushkit = "PushKit"
    case callkit = "CallKit"
    case audio = "Audio"
    case network = "Network"
    case ui = "UI"
    case general = "General"
    
    var subsystem: String {
        return "com.voipreceiver.app"
    }
}

// MARK: - Log Entry Model
struct LogEntry {
    let timestamp: Date
    let level: LogLevel
    let category: LogCategory
    let message: String
    let file: String
    let function: String
    let line: Int
    
    var formattedMessage: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss.SSS"
        let timeString = formatter.string(from: timestamp)
        
        let fileName = (file as NSString).lastPathComponent
        return "\(timeString) \(level.emoji) [\(category.rawValue)] \(fileName):\(line) \(function) - \(message)"
    }
}

// MARK: - Logging Manager
class LoggingManager {
    static let shared = LoggingManager()
    
    // OS Log instances for each category
    private var osLogs: [LogCategory: OSLog] = [:]
    
    // In-memory log storage
    private var logEntries: [LogEntry] = []
    private let maxLogEntries = 1000
    private let logQueue = DispatchQueue(label: "com.voipreceiver.logging", qos: .utility)
    
    // File logging
    private var logFileURL: URL?
    private let maxLogFileSize: Int = 10 * 1024 * 1024 // 10MB
    
    // Configuration
    private var minimumLogLevel: LogLevel = .debug
    private var isFileLoggingEnabled = true
    private var isConsoleLoggingEnabled = true
    
    private init() {
        setupOSLogs()
        setupFileLogging()
    }
    
    // MARK: - Public Methods
    
    func log(_ message: String, 
             level: LogLevel = .info, 
             category: LogCategory = .general,
             file: String = #file,
             function: String = #function,
             line: Int = #line) {
        
        // Check if we should log this level
        guard shouldLog(level: level) else { return }
        
        let logEntry = LogEntry(
            timestamp: Date(),
            level: level,
            category: category,
            message: message,
            file: file,
            function: function,
            line: line
        )
        
        logQueue.async { [weak self] in
            self?.processLogEntry(logEntry)
        }
    }
    
    func setMinimumLogLevel(_ level: LogLevel) {
        minimumLogLevel = level
        log("Minimum log level set to: \(level.rawValue)", level: .info, category: .general)
    }
    
    func enableFileLogging(_ enabled: Bool) {
        isFileLoggingEnabled = enabled
        log("File logging \(enabled ? "enabled" : "disabled")", level: .info, category: .general)
    }
    
    func enableConsoleLogging(_ enabled: Bool) {
        isConsoleLoggingEnabled = enabled
        log("Console logging \(enabled ? "enabled" : "disabled")", level: .info, category: .general)
    }
    
    func getRecentLogs(count: Int = 100) -> [LogEntry] {
        return logQueue.sync {
            return Array(logEntries.suffix(count))
        }
    }
    
    func getLogsForCategory(_ category: LogCategory, count: Int = 100) -> [LogEntry] {
        return logQueue.sync {
            let filteredLogs = logEntries.filter { $0.category == category }
            return Array(filteredLogs.suffix(count))
        }
    }
    
    func getLogsForLevel(_ level: LogLevel, count: Int = 100) -> [LogEntry] {
        return logQueue.sync {
            let filteredLogs = logEntries.filter { $0.level == level }
            return Array(filteredLogs.suffix(count))
        }
    }
    
    func exportLogs() -> String {
        return logQueue.sync {
            return logEntries.map { $0.formattedMessage }.joined(separator: "\n")
        }
    }
    
    func clearLogs() {
        logQueue.async { [weak self] in
            self?.logEntries.removeAll()
            self?.log("Log entries cleared", level: .info, category: .general)
        }
    }
    
    func getLogFileURL() -> URL? {
        return logFileURL
    }
    
    // MARK: - Convenience Methods
    
    func debug(_ message: String, category: LogCategory = .general, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, level: .debug, category: category, file: file, function: function, line: line)
    }
    
    func info(_ message: String, category: LogCategory = .general, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, level: .info, category: category, file: file, function: function, line: line)
    }
    
    func warning(_ message: String, category: LogCategory = .general, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, level: .warning, category: category, file: file, function: function, line: line)
    }
    
    func error(_ message: String, category: LogCategory = .general, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, level: .error, category: category, file: file, function: function, line: line)
    }
    
    func critical(_ message: String, category: LogCategory = .general, file: String = #file, function: String = #function, line: Int = #line) {
        log(message, level: .critical, category: category, file: file, function: function, line: line)
    }

    // MARK: - Private Methods

    private func setupOSLogs() {
        for category in LogCategory.allCases {
            osLogs[category] = OSLog(subsystem: category.subsystem, category: category.rawValue)
        }
    }

    private func setupFileLogging() {
        guard let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first else {
            print("Failed to get documents directory for logging")
            return
        }

        logFileURL = documentsDirectory.appendingPathComponent("voip_logs.txt")

        // Create log file if it doesn't exist
        if let logFileURL = logFileURL, !FileManager.default.fileExists(atPath: logFileURL.path) {
            FileManager.default.createFile(atPath: logFileURL.path, contents: nil, attributes: nil)
        }
    }

    private func shouldLog(level: LogLevel) -> Bool {
        let levelPriority = LogLevel.allCases.firstIndex(of: level) ?? 0
        let minimumPriority = LogLevel.allCases.firstIndex(of: minimumLogLevel) ?? 0
        return levelPriority >= minimumPriority
    }

    private func processLogEntry(_ entry: LogEntry) {
        // Add to in-memory storage
        logEntries.append(entry)

        // Maintain maximum log entries
        if logEntries.count > maxLogEntries {
            logEntries.removeFirst(logEntries.count - maxLogEntries)
        }

        // Log to console
        if isConsoleLoggingEnabled {
            logToConsole(entry)
        }

        // Log to OS Log
        logToOSLog(entry)

        // Log to file
        if isFileLoggingEnabled {
            logToFile(entry)
        }
    }

    private func logToConsole(_ entry: LogEntry) {
        print(entry.formattedMessage)
    }

    private func logToOSLog(_ entry: LogEntry) {
        guard let osLog = osLogs[entry.category] else { return }

        let message = "\(entry.function) - \(entry.message)"
        os_log("%{public}@", log: osLog, type: entry.level.osLogType, message)
    }

    private func logToFile(_ entry: LogEntry) {
        guard let logFileURL = logFileURL else { return }

        let logLine = entry.formattedMessage + "\n"

        do {
            // Check file size and rotate if necessary
            let fileAttributes = try FileManager.default.attributesOfItem(atPath: logFileURL.path)
            if let fileSize = fileAttributes[.size] as? Int, fileSize > maxLogFileSize {
                rotateLogFile()
            }

            // Append to log file
            if let data = logLine.data(using: .utf8) {
                if FileManager.default.fileExists(atPath: logFileURL.path) {
                    let fileHandle = try FileHandle(forWritingTo: logFileURL)
                    fileHandle.seekToEndOfFile()
                    fileHandle.write(data)
                    fileHandle.closeFile()
                } else {
                    try data.write(to: logFileURL)
                }
            }
        } catch {
            print("Failed to write to log file: \(error)")
        }
    }

    private func rotateLogFile() {
        guard let logFileURL = logFileURL else { return }

        let backupURL = logFileURL.appendingPathExtension("backup")

        do {
            // Remove old backup if it exists
            if FileManager.default.fileExists(atPath: backupURL.path) {
                try FileManager.default.removeItem(at: backupURL)
            }

            // Move current log to backup
            try FileManager.default.moveItem(at: logFileURL, to: backupURL)

            // Create new log file
            FileManager.default.createFile(atPath: logFileURL.path, contents: nil, attributes: nil)

            log("Log file rotated", level: .info, category: .general)
        } catch {
            print("Failed to rotate log file: \(error)")
        }
    }
}
