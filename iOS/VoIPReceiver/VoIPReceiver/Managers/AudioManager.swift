import Foundation
import AVFoundation
import MediaPlayer

// MARK: - Audio Route Types
enum AudioRoute: String, CaseIterable {
    case speaker = "speaker"
    case receiver = "receiver"
    case bluetooth = "bluetooth"
    case headphones = "headphones"
    case airPods = "airpods"
    case carPlay = "carplay"
    
    var displayName: String {
        switch self {
        case .speaker: return "Speaker"
        case .receiver: return "iPhone"
        case .bluetooth: return "Bluetooth"
        case .headphones: return "Headphones"
        case .airPods: return "AirPods"
        case .carPlay: return "CarPlay"
        }
    }
}

// MARK: - Audio Manager Delegate Protocol
protocol AudioManagerDelegate: AnyObject {
    func audioManager(_ manager: AudioManager, didChangeRoute route: AudioRoute)
    func audioManager(_ manager: AudioManager, didEncounterError error: AudioError)
    func audioManager(_ manager: AudioManager, didUpdateVolume volume: Float)
}

// MARK: - Audio Error Types
enum AudioError: Error, LocalizedError {
    case sessionConfigurationFailed(String)
    case routeChangeFailed(String)
    case activationFailed(String)
    case deactivationFailed(String)
    case interruptionHandlingFailed(String)
    
    var errorDescription: String? {
        switch self {
        case .sessionConfigurationFailed(let message):
            return "Audio session configuration failed: \(message)"
        case .routeChangeFailed(let message):
            return "Audio route change failed: \(message)"
        case .activationFailed(let message):
            return "Audio session activation failed: \(message)"
        case .deactivationFailed(let message):
            return "Audio session deactivation failed: \(message)"
        case .interruptionHandlingFailed(let message):
            return "Audio interruption handling failed: \(message)"
        }
    }
}

// MARK: - Audio Manager
class AudioManager: NSObject {
    static let shared = AudioManager()
    
    weak var delegate: AudioManagerDelegate?
    
    private let audioSession = AVAudioSession.sharedInstance()
    private var isAudioSessionActive = false
    private var currentRoute: AudioRoute = .receiver
    
    // Volume monitoring
    private var volumeView: MPVolumeView?
    private var volumeSlider: UISlider?
    
    private override init() {
        super.init()
        setupAudioSession()
        setupVolumeMonitoring()
        setupNotifications()
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - Public Methods
    
    func setupAudioSession() {
        LoggingManager.shared.log("Setting up audio session", level: .info)
        
        do {
            // Configure audio session for VoIP
            try audioSession.setCategory(.playAndRecord, 
                                       mode: .voiceChat, 
                                       options: [.allowBluetooth, .allowBluetoothA2DP, .allowAirPlay])
            
            // Set preferred sample rate and buffer duration
            try audioSession.setPreferredSampleRate(16000.0)
            try audioSession.setPreferredIOBufferDuration(0.02) // 20ms
            
            LoggingManager.shared.log("Audio session configured successfully", level: .info)
            
        } catch {
            let audioError = AudioError.sessionConfigurationFailed(error.localizedDescription)
            LoggingManager.shared.log("Audio session setup failed: \(error)", level: .error)
            delegate?.audioManager(self, didEncounterError: audioError)
        }
    }
    
    func activateAudioSession() {
        guard !isAudioSessionActive else {
            LoggingManager.shared.log("Audio session already active", level: .info)
            return
        }
        
        LoggingManager.shared.log("Activating audio session", level: .info)
        
        do {
            try audioSession.setActive(true)
            isAudioSessionActive = true
            updateCurrentRoute()
            LoggingManager.shared.log("Audio session activated successfully", level: .info)
            
        } catch {
            let audioError = AudioError.activationFailed(error.localizedDescription)
            LoggingManager.shared.log("Audio session activation failed: \(error)", level: .error)
            delegate?.audioManager(self, didEncounterError: audioError)
        }
    }
    
    func deactivateAudioSession() {
        guard isAudioSessionActive else {
            LoggingManager.shared.log("Audio session already inactive", level: .info)
            return
        }
        
        LoggingManager.shared.log("Deactivating audio session", level: .info)
        
        do {
            try audioSession.setActive(false, options: .notifyOthersOnDeactivation)
            isAudioSessionActive = false
            LoggingManager.shared.log("Audio session deactivated successfully", level: .info)
            
        } catch {
            let audioError = AudioError.deactivationFailed(error.localizedDescription)
            LoggingManager.shared.log("Audio session deactivation failed: \(error)", level: .error)
            delegate?.audioManager(self, didEncounterError: audioError)
        }
    }
    
    func setAudioRoute(_ route: AudioRoute) {
        LoggingManager.shared.log("Setting audio route to: \(route.displayName)", level: .info)
        
        do {
            switch route {
            case .speaker:
                try audioSession.overrideOutputAudioPort(.speaker)
            case .receiver:
                try audioSession.overrideOutputAudioPort(.none)
            default:
                // For Bluetooth, headphones, etc., the system handles routing automatically
                try audioSession.overrideOutputAudioPort(.none)
            }
            
            currentRoute = route
            delegate?.audioManager(self, didChangeRoute: route)
            LoggingManager.shared.log("Audio route set successfully", level: .info)
            
        } catch {
            let audioError = AudioError.routeChangeFailed(error.localizedDescription)
            LoggingManager.shared.log("Audio route change failed: \(error)", level: .error)
            delegate?.audioManager(self, didEncounterError: audioError)
        }
    }
    
    func getCurrentRoute() -> AudioRoute {
        return currentRoute
    }
    
    func getAvailableRoutes() -> [AudioRoute] {
        var routes: [AudioRoute] = [.receiver, .speaker]
        
        let currentRouteOutputs = audioSession.currentRoute.outputs
        
        for output in currentRouteOutputs {
            switch output.portType {
            case .bluetoothA2DP, .bluetoothHFP, .bluetoothLE:
                if !routes.contains(.bluetooth) {
                    routes.append(.bluetooth)
                }
            case .headphones:
                if !routes.contains(.headphones) {
                    routes.append(.headphones)
                }
            case .airPlay:
                if !routes.contains(.airPods) {
                    routes.append(.airPods)
                }
            case .carAudio:
                if !routes.contains(.carPlay) {
                    routes.append(.carPlay)
                }
            default:
                break
            }
        }
        
        return routes
    }
    
    func toggleSpeaker() {
        let newRoute: AudioRoute = (currentRoute == .speaker) ? .receiver : .speaker
        setAudioRoute(newRoute)
    }
    
    func isSpeakerEnabled() -> Bool {
        return currentRoute == .speaker
    }
    
    func setVolume(_ volume: Float) {
        guard let volumeSlider = volumeSlider else { return }
        
        volumeSlider.value = volume
        LoggingManager.shared.log("Volume set to: \(volume)", level: .info)
    }
    
    func getCurrentVolume() -> Float {
        return audioSession.outputVolume
    }

    // MARK: - Private Methods

    private func setupVolumeMonitoring() {
        volumeView = MPVolumeView(frame: CGRect.zero)
        volumeView?.isHidden = true

        // Find the volume slider
        for subview in volumeView?.subviews ?? [] {
            if let slider = subview as? UISlider {
                volumeSlider = slider
                break
            }
        }
    }

    private func setupNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleRouteChange),
            name: AVAudioSession.routeChangeNotification,
            object: nil
        )

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleInterruption),
            name: AVAudioSession.interruptionNotification,
            object: nil
        )

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleVolumeChange),
            name: NSNotification.Name(rawValue: "AVSystemController_SystemVolumeDidChangeNotification"),
            object: nil
        )
    }

    private func updateCurrentRoute() {
        let currentRouteOutputs = audioSession.currentRoute.outputs

        guard let output = currentRouteOutputs.first else {
            currentRoute = .receiver
            return
        }

        switch output.portType {
        case .builtInSpeaker:
            currentRoute = .speaker
        case .builtInReceiver:
            currentRoute = .receiver
        case .bluetoothA2DP, .bluetoothHFP, .bluetoothLE:
            currentRoute = .bluetooth
        case .headphones:
            currentRoute = .headphones
        case .airPlay:
            currentRoute = .airPods
        case .carAudio:
            currentRoute = .carPlay
        default:
            currentRoute = .receiver
        }

        LoggingManager.shared.log("Current audio route: \(currentRoute.displayName)", level: .info)
    }

    // MARK: - Notification Handlers

    @objc private func handleRouteChange(notification: Notification) {
        guard let userInfo = notification.userInfo,
              let reasonValue = userInfo[AVAudioSessionRouteChangeReasonKey] as? UInt,
              let reason = AVAudioSession.RouteChangeReason(rawValue: reasonValue) else {
            return
        }

        LoggingManager.shared.log("Audio route changed, reason: \(reason.rawValue)", level: .info)

        updateCurrentRoute()
        delegate?.audioManager(self, didChangeRoute: currentRoute)

        switch reason {
        case .newDeviceAvailable:
            LoggingManager.shared.log("New audio device available", level: .info)
        case .oldDeviceUnavailable:
            LoggingManager.shared.log("Audio device disconnected", level: .info)
        case .categoryChange:
            LoggingManager.shared.log("Audio category changed", level: .info)
        case .override:
            LoggingManager.shared.log("Audio route override", level: .info)
        case .wakeFromSleep:
            LoggingManager.shared.log("Audio route wake from sleep", level: .info)
        case .noSuitableRouteForCategory:
            LoggingManager.shared.log("No suitable route for category", level: .warning)
        case .routeConfigurationChange:
            LoggingManager.shared.log("Route configuration changed", level: .info)
        default:
            LoggingManager.shared.log("Unknown route change reason", level: .info)
        }
    }

    @objc private func handleInterruption(notification: Notification) {
        guard let userInfo = notification.userInfo,
              let typeValue = userInfo[AVAudioSessionInterruptionTypeKey] as? UInt,
              let type = AVAudioSession.InterruptionType(rawValue: typeValue) else {
            return
        }

        switch type {
        case .began:
            LoggingManager.shared.log("Audio interruption began", level: .warning)
            // Handle interruption (pause audio, etc.)

        case .ended:
            LoggingManager.shared.log("Audio interruption ended", level: .info)

            if let optionsValue = userInfo[AVAudioSessionInterruptionOptionKey] as? UInt {
                let options = AVAudioSession.InterruptionOptions(rawValue: optionsValue)
                if options.contains(.shouldResume) {
                    LoggingManager.shared.log("Should resume audio after interruption", level: .info)
                    // Resume audio if appropriate
                }
            }

        @unknown default:
            LoggingManager.shared.log("Unknown interruption type", level: .warning)
        }
    }

    @objc private func handleVolumeChange(notification: Notification) {
        let volume = getCurrentVolume()
        LoggingManager.shared.log("Volume changed to: \(volume)", level: .info)
        delegate?.audioManager(self, didUpdateVolume: volume)
    }
}
