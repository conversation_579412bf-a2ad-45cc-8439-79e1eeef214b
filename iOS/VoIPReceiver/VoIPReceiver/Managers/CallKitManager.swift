import Foundation
import CallKit
import AVFoundation

// MARK: - CallKit Manager Delegate Protocol
protocol CallKitManagerDelegate: AnyObject {
    func callKitManager(_ manager: Call<PERSON>itMana<PERSON>, didAnswerCall uuid: UUID)
    func callKitManager(_ manager: <PERSON><PERSON>it<PERSON><PERSON><PERSON>, didEndCall uuid: UUID)
    func callKitManager(_ manager: CallKitManager, didHoldCall uuid: UUID, onHold: Bool)
    func callKitManager(_ manager: CallKitManager, didMuteCall uuid: UUID, muted: Bool)
    func callKitManager(_ manager: CallKitManager, didActivateAudioSession audioSession: AVAudioSession)
    func callKitManager(_ manager: CallKitManager, didDeactivateAudioSession audioSession: AVAudioSession)
}

// MARK: - CallKit Manager
class CallKitManager: NSObject {
    static let shared = CallKitManager()
    
    weak var delegate: CallKitManagerDelegate?
    
    private let callController = CXCallController()
    private var provider: CXProvider
    
    // Track active calls
    private var activeCalls: [UUID: CXCall] = [:]
    
    private override init() {
        // Configure provider
        let providerConfiguration = CXProviderConfiguration(localizedName: "VoIP Receiver")
        providerConfiguration.supportsVideo = false
        providerConfiguration.maximumCallsPerCallGroup = 1
        providerConfiguration.maximumCallGroups = 1
        providerConfiguration.supportedHandleTypes = [.phoneNumber, .generic]
        
        // Set audio configuration
        providerConfiguration.audioSessionID = AVAudioSession.sharedInstance().audioSessionID
        
        // Set ringtone
        if let ringtonePath = Bundle.main.path(forResource: "ringtone", ofType: "caf") {
            providerConfiguration.ringtoneSound = ringtonePath
        }
        
        provider = CXProvider(configuration: providerConfiguration)
        
        super.init()
        
        provider.setDelegate(self, queue: nil)
        
        LoggingManager.shared.log("CallKit manager initialized", level: .info)
    }
    
    // MARK: - Public Methods
    
    func reportIncomingCall(uuid: UUID, handle: String, hasVideo: Bool = false, completion: @escaping (Error?) -> Void) {
        LoggingManager.shared.log("Reporting incoming call: \(handle)", level: .info)
        
        let callHandle = CXHandle(type: .phoneNumber, value: handle)
        let callUpdate = CXCallUpdate()
        callUpdate.remoteHandle = callHandle
        callUpdate.hasVideo = hasVideo
        callUpdate.localizedCallerName = handle
        callUpdate.supportsHolding = true
        callUpdate.supportsGrouping = false
        callUpdate.supportsUngrouping = false
        callUpdate.supportsDTMF = true
        
        provider.reportNewIncomingCall(with: uuid, update: callUpdate) { [weak self] error in
            if let error = error {
                LoggingManager.shared.log("Failed to report incoming call: \(error)", level: .error)
                completion(error)
            } else {
                LoggingManager.shared.log("Successfully reported incoming call", level: .info)
                completion(nil)
            }
        }
    }
    
    func updateCall(uuid: UUID, connected: Bool) {
        LoggingManager.shared.log("Updating call \(uuid) connected status: \(connected)", level: .info)
        
        let callUpdate = CXCallUpdate()
        callUpdate.hasVideo = false
        
        if connected {
            provider.reportOutgoingCall(with: uuid, connectedAt: Date())
        }
        
        provider.reportCall(with: uuid, updated: callUpdate)
    }
    
    func endCall(uuid: UUID) {
        LoggingManager.shared.log("Ending call: \(uuid)", level: .info)
        
        let endCallAction = CXEndCallAction(call: uuid)
        let transaction = CXTransaction(action: endCallAction)
        
        callController.request(transaction) { [weak self] error in
            if let error = error {
                LoggingManager.shared.log("Failed to end call: \(error)", level: .error)
            } else {
                LoggingManager.shared.log("Successfully ended call", level: .info)
                self?.activeCalls.removeValue(forKey: uuid)
            }
        }
    }
    
    func setCallOnHold(uuid: UUID, onHold: Bool) {
        LoggingManager.shared.log("Setting call \(uuid) on hold: \(onHold)", level: .info)
        
        let holdAction = CXSetHeldCallAction(call: uuid, onHold: onHold)
        let transaction = CXTransaction(action: holdAction)
        
        callController.request(transaction) { error in
            if let error = error {
                LoggingManager.shared.log("Failed to set call hold status: \(error)", level: .error)
            } else {
                LoggingManager.shared.log("Successfully set call hold status", level: .info)
            }
        }
    }
    
    func muteCall(uuid: UUID, muted: Bool) {
        LoggingManager.shared.log("Setting call \(uuid) mute status: \(muted)", level: .info)
        
        let muteAction = CXSetMutedCallAction(call: uuid, muted: muted)
        let transaction = CXTransaction(action: muteAction)
        
        callController.request(transaction) { error in
            if let error = error {
                LoggingManager.shared.log("Failed to set call mute status: \(error)", level: .error)
            } else {
                LoggingManager.shared.log("Successfully set call mute status", level: .info)
            }
        }
    }
    
    func reportCallEnded(uuid: UUID, reason: CXCallEndedReason) {
        LoggingManager.shared.log("Reporting call ended: \(uuid), reason: \(reason.rawValue)", level: .info)
        
        provider.reportCall(with: uuid, endedAt: Date(), reason: reason)
        activeCalls.removeValue(forKey: uuid)
    }
    
    func reportCallFailed(uuid: UUID, error: Error) {
        LoggingManager.shared.log("Reporting call failed: \(uuid), error: \(error)", level: .error)
        
        provider.reportCall(with: uuid, endedAt: Date(), reason: .failed)
        activeCalls.removeValue(forKey: uuid)
    }
    
    // MARK: - Private Methods
    
    private func configureAudioSession() {
        let audioSession = AVAudioSession.sharedInstance()
        
        do {
            try audioSession.setCategory(.playAndRecord, mode: .voiceChat, options: [.allowBluetooth])
            try audioSession.setActive(true)
            LoggingManager.shared.log("Audio session configured for call", level: .info)
        } catch {
            LoggingManager.shared.log("Failed to configure audio session: \(error)", level: .error)
        }
    }
    
    private func deactivateAudioSession() {
        let audioSession = AVAudioSession.sharedInstance()

        do {
            try audioSession.setActive(false, options: .notifyOthersOnDeactivation)
            LoggingManager.shared.log("Audio session deactivated", level: .info)
        } catch {
            LoggingManager.shared.log("Failed to deactivate audio session: \(error)", level: .error)
        }
    }
}

// MARK: - CXProviderDelegate
extension CallKitManager: CXProviderDelegate {

    func providerDidReset(_ provider: CXProvider) {
        LoggingManager.shared.log("CallKit provider reset", level: .warning)

        // End all active calls
        for (uuid, _) in activeCalls {
            reportCallEnded(uuid: uuid, reason: .failed)
        }

        activeCalls.removeAll()
    }

    func provider(_ provider: CXProvider, perform action: CXStartCallAction) {
        LoggingManager.shared.log("CallKit start call action", level: .info)

        configureAudioSession()
        action.fulfill()
    }

    func provider(_ provider: CXProvider, perform action: CXAnswerCallAction) {
        LoggingManager.shared.log("CallKit answer call action: \(action.callUUID)", level: .info)

        configureAudioSession()

        // Notify delegate
        delegate?.callKitManager(self, didAnswerCall: action.callUUID)

        action.fulfill()
    }

    func provider(_ provider: CXProvider, perform action: CXEndCallAction) {
        LoggingManager.shared.log("CallKit end call action: \(action.callUUID)", level: .info)

        // Notify delegate
        delegate?.callKitManager(self, didEndCall: action.callUUID)

        deactivateAudioSession()
        activeCalls.removeValue(forKey: action.callUUID)

        action.fulfill()
    }

    func provider(_ provider: CXProvider, perform action: CXSetHeldCallAction) {
        LoggingManager.shared.log("CallKit hold call action: \(action.callUUID), onHold: \(action.isOnHold)", level: .info)

        // Notify delegate
        delegate?.callKitManager(self, didHoldCall: action.callUUID, onHold: action.isOnHold)

        action.fulfill()
    }

    func provider(_ provider: CXProvider, perform action: CXSetMutedCallAction) {
        LoggingManager.shared.log("CallKit mute call action: \(action.callUUID), muted: \(action.isMuted)", level: .info)

        // Notify delegate
        delegate?.callKitManager(self, didMuteCall: action.callUUID, muted: action.isMuted)

        action.fulfill()
    }

    func provider(_ provider: CXProvider, didActivate audioSession: AVAudioSession) {
        LoggingManager.shared.log("CallKit audio session activated", level: .info)

        // Notify delegate
        delegate?.callKitManager(self, didActivateAudioSession: audioSession)
    }

    func provider(_ provider: CXProvider, didDeactivate audioSession: AVAudioSession) {
        LoggingManager.shared.log("CallKit audio session deactivated", level: .info)

        // Notify delegate
        delegate?.callKitManager(self, didDeactivateAudioSession: audioSession)
    }

    func provider(_ provider: CXProvider, timedOutPerforming action: CXAction) {
        LoggingManager.shared.log("CallKit action timed out: \(action)", level: .error)
        action.fail()
    }
}
