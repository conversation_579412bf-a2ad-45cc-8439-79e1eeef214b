import Foundation
import PushKit
import UIKit

// MARK: - PushKit Manager Delegate Protocol
protocol PushKitManagerDelegate: AnyObject {
    func pushKitManager(_ manager: PushKitManager, didReceiveIncomingCall callInfo: [String: Any])
    func pushKitManager(_ manager: PushKitManager, didUpdateCredentials credentials: PKPushCredentials)
    func pushKitManager(_ manager: PushKitManager, didInvalidateCredentials credentials: PKPushCredentials)
}

// MARK: - PushKit Manager
class PushKitManager: NSObject {
    static let shared = PushKitManager()
    
    weak var delegate: PushKitManagerDelegate?
    
    private var pushRegistry: PKPushRegistry?
    private var currentCredentials: PKPushCredentials?
    
    private override init() {
        super.init()
        setupPushRegistry()
    }
    
    // MARK: - Public Methods
    
    func registerForVoIPPushes() {
        LoggingManager.shared.log("Registering for VoIP push notifications", level: .info)
        
        guard let pushRegistry = pushRegistry else {
            LoggingManager.shared.log("Push registry not available", level: .error)
            return
        }
        
        // Set desired push types
        pushRegistry.desiredPushTypes = [.voIP]
        
        LoggingManager.shared.log("VoIP push registration initiated", level: .info)
    }
    
    func unregisterFromVoIPPushes() {
        LoggingManager.shared.log("Unregistering from VoIP push notifications", level: .info)
        
        guard let pushRegistry = pushRegistry else {
            LoggingManager.shared.log("Push registry not available", level: .error)
            return
        }
        
        // Remove VoIP push type
        pushRegistry.desiredPushTypes = nil
        currentCredentials = nil
        
        LoggingManager.shared.log("VoIP push unregistration completed", level: .info)
    }
    
    func getCurrentPushToken() -> String? {
        guard let credentials = currentCredentials else {
            return nil
        }
        
        let token = credentials.token.map { String(format: "%02.2hhx", $0) }.joined()
        return token
    }
    
    // MARK: - Private Methods
    
    private func setupPushRegistry() {
        pushRegistry = PKPushRegistry(queue: DispatchQueue.main)
        pushRegistry?.delegate = self
        
        LoggingManager.shared.log("PushKit registry setup completed", level: .info)
    }
    
    private func handleIncomingVoIPPush(payload: [AnyHashable: Any]) {
        LoggingManager.shared.log("Processing VoIP push payload: \(payload)", level: .info)
        
        // Extract call information from payload
        var callInfo: [String: Any] = [:]
        
        if let callerID = payload["caller_id"] as? String {
            callInfo["caller_id"] = callerID
        }
        
        if let callerName = payload["caller_name"] as? String {
            callInfo["caller_name"] = callerName
        }
        
        if let callUUID = payload["call_uuid"] as? String {
            callInfo["call_uuid"] = callUUID
        }
        
        if let serverInfo = payload["server_info"] as? [String: Any] {
            callInfo["server_info"] = serverInfo
        }
        
        // Add timestamp
        callInfo["received_at"] = Date().timeIntervalSince1970
        
        // Notify delegate
        delegate?.pushKitManager(self, didReceiveIncomingCall: callInfo)
    }
    
    private func validatePushPayload(_ payload: [AnyHashable: Any]) -> Bool {
        // Validate required fields
        guard payload["caller_id"] is String else {
            LoggingManager.shared.log("Invalid push payload - missing caller_id", level: .error)
            return false
        }
        
        return true
    }
}

// MARK: - PKPushRegistryDelegate
extension PushKitManager: PKPushRegistryDelegate {
    
    func pushRegistry(_ registry: PKPushRegistry, didUpdate pushCredentials: PKPushCredentials, for type: PKPushType) {
        LoggingManager.shared.log("PushKit credentials updated for type: \(type.rawValue)", level: .info)
        
        guard type == .voIP else {
            LoggingManager.shared.log("Ignoring non-VoIP push credentials", level: .warning)
            return
        }
        
        currentCredentials = pushCredentials
        
        let token = pushCredentials.token.map { String(format: "%02.2hhx", $0) }.joined()
        LoggingManager.shared.log("VoIP push token: \(token)", level: .info)
        
        // Notify delegate
        delegate?.pushKitManager(self, didUpdateCredentials: pushCredentials)
    }
    
    func pushRegistry(_ registry: PKPushRegistry, didInvalidatePushTokenFor type: PKPushType) {
        LoggingManager.shared.log("PushKit token invalidated for type: \(type.rawValue)", level: .warning)
        
        guard type == .voIP else {
            return
        }
        
        if let credentials = currentCredentials {
            delegate?.pushKitManager(self, didInvalidateCredentials: credentials)
        }
        
        currentCredentials = nil
    }
    
    func pushRegistry(_ registry: PKPushRegistry, didReceiveIncomingPushWith payload: PKPushPayload, for type: PKPushType, completion: @escaping () -> Void) {
        LoggingManager.shared.log("Received VoIP push notification", level: .info)
        
        guard type == .voIP else {
            LoggingManager.shared.log("Ignoring non-VoIP push", level: .warning)
            completion()
            return
        }
        
        // Validate payload
        guard validatePushPayload(payload.dictionaryPayload) else {
            LoggingManager.shared.log("Invalid VoIP push payload", level: .error)
            completion()
            return
        }
        
        // Handle the incoming call
        handleIncomingVoIPPush(payload: payload.dictionaryPayload)
        
        // Complete the push handling
        completion()
    }
    
    // iOS 13+ method for handling VoIP pushes
    @available(iOS 13.0, *)
    func pushRegistry(_ registry: PKPushRegistry, didReceiveIncomingPushWith payload: PKPushPayload, for type: PKPushType) {
        LoggingManager.shared.log("Received VoIP push notification (iOS 13+)", level: .info)
        
        guard type == .voIP else {
            LoggingManager.shared.log("Ignoring non-VoIP push", level: .warning)
            return
        }
        
        // Validate payload
        guard validatePushPayload(payload.dictionaryPayload) else {
            LoggingManager.shared.log("Invalid VoIP push payload", level: .error)
            return
        }
        
        // Handle the incoming call
        handleIncomingVoIPPush(payload: payload.dictionaryPayload)
    }
}

// MARK: - Push Payload Helper
extension PushKitManager {
    
    struct VoIPPushPayload {
        let callerID: String
        let callerName: String?
        let callUUID: String?
        let serverInfo: [String: Any]?
        let receivedAt: Date
        
        init?(from payload: [AnyHashable: Any]) {
            guard let callerID = payload["caller_id"] as? String else {
                return nil
            }
            
            self.callerID = callerID
            self.callerName = payload["caller_name"] as? String
            self.callUUID = payload["call_uuid"] as? String
            self.serverInfo = payload["server_info"] as? [String: Any]
            self.receivedAt = Date()
        }
    }
    
    func parseVoIPPayload(_ payload: [AnyHashable: Any]) -> VoIPPushPayload? {
        return VoIPPushPayload(from: payload)
    }
}
