# VoIP Receiver iOS App

A comprehensive iOS application that receives VoIP calls forwarded from an Android phone. The iPhone does NOT require a SIM card as all calls arrive over the internet using SIP or WebRTC protocols.

## Features

### ✅ Core VoIP Integration
- **SIP/WebRTC Support**: Configurable to work with various VoIP backends
- **Internet-Only Calls**: No SIM card required on iPhone
- **Audio Streaming**: Bidirectional audio with quality monitoring
- **Multiple Codec Support**: Configurable audio codecs

### ✅ Push Notifications
- **PushKit Integration**: VoIP push notifications to wake app
- **Background Call Handling**: Receives calls when app is terminated
- **Reliable Wake-up**: Ensures calls are never missed

### ✅ CallKit Integration
- **Native Call Interface**: Appears like regular phone calls
- **Lock Screen Support**: Answer calls from lock screen
- **Call Controls**: Hold, mute, speaker, end call
- **Caller ID Display**: Shows caller information when available

### ✅ Permissions & Background Modes
- **Microphone Access**: Automatic permission handling
- **Background VoIP**: Maintains connection in background
- **Audio Session Management**: Proper audio routing and interruption handling

### ✅ User Interface
- **Call Availability Toggle**: Enable/disable VoIP service
- **Real-time Call Status**: Live call information and duration
- **Audio Route Selection**: Speaker, Bluetooth, headphones
- **Call Quality Indicator**: Visual quality feedback

### ✅ Call History & Logging
- **Local Call History**: Persistent storage of call records
- **Comprehensive Logging**: Debug-friendly logging system
- **Call Statistics**: Analytics and usage metrics
- **Export Functionality**: Share logs and history

### ✅ Background Support
- **App Lifecycle Management**: Handles background/foreground transitions
- **Background Tasks**: Maintains VoIP connection
- **Network Monitoring**: Automatic reconnection on network changes

## Project Structure

```
VoIPReceiver/
├── VoIPReceiver/
│   ├── AppDelegate.swift              # Main app delegate with VoIP setup
│   ├── SceneDelegate.swift            # Scene management
│   ├── ViewController.swift           # Main UI controller
│   ├── Models/
│   │   ├── CallSession.swift          # Call session data model
│   │   └── CallHistory.swift          # Call history management
│   ├── Managers/
│   │   ├── VoIPManager.swift          # Core VoIP functionality
│   │   ├── PushKitManager.swift       # VoIP push notifications
│   │   ├── CallKitManager.swift       # Native call interface
│   │   ├── AudioManager.swift         # Audio session management
│   │   └── LoggingManager.swift       # Comprehensive logging
│   ├── Views/
│   │   ├── Main.storyboard           # Main UI layout
│   │   └── CallViewController.swift   # Call history UI
│   └── Supporting Files/
│       ├── Info.plist                # App configuration
│       └── VoIPReceiver.entitlements # App entitlements
└── Podfile                           # Dependencies
```

## Setup Instructions

### 1. Prerequisites
- Xcode 14.0 or later
- iOS 13.0 or later target device
- Apple Developer Account (for VoIP entitlements)
- CocoaPods installed

### 2. Installation

```bash
# Clone or download the project
cd iOS/VoIPReceiver

# Install dependencies
pod install

# Open the workspace (not the .xcodeproj)
open VoIPReceiver.xcworkspace
```

### 3. Configuration

#### A. Apple Developer Setup
1. **Enable VoIP Push Notifications**:
   - Go to Apple Developer Console
   - Select your App ID
   - Enable "Push Notifications" capability
   - Enable "Background Modes" with VoIP

2. **Create VoIP Push Certificate**:
   - Create a VoIP Services Certificate
   - Download and install in Keychain
   - Export as .p12 for your backend server

#### B. App Configuration
1. **Update Bundle Identifier**:
   ```swift
   // In project settings, change bundle identifier to your own
   com.yourcompany.voipreceiver
   ```

2. **Configure VoIP Backend**:
   ```swift
   // In VoIPManager.swift, update server configuration
   let defaultConfig = VoIPConfiguration(
       serverAddress: "your-sip-server.com",
       serverPort: 5060,
       username: "your-username",
       password: "your-password",
       domain: "your-domain.com",
       transport: .udp,
       enableSTUN: true,
       stunServer: "stun:stun.l.google.com:19302",
       enableICE: true
   )
   ```

### 4. VoIP Library Integration

Choose one of the following VoIP libraries and uncomment in Podfile:

#### Option A: Linphone SDK (Recommended)
```ruby
pod 'linphone-sdk', '~> 5.2.0'
```

#### Option B: PJSIP
```ruby
pod 'PJSIP', '~> 2.13'
```

#### Option C: WebRTC
```ruby
pod 'GoogleWebRTC', '~> 1.1.31999'
```

### 5. Backend Server Requirements

Your backend server needs to:

1. **Handle VoIP Push Notifications**:
   ```json
   {
     "aps": {
       "alert": "Incoming call",
       "sound": "default"
     },
     "caller_id": "+**********",
     "caller_name": "John Doe",
     "call_uuid": "unique-call-id",
     "server_info": {
       "sip_server": "your-server.com",
       "port": 5060
     }
   }
   ```

2. **SIP/WebRTC Bridge**: Forward calls from Android to iOS
3. **Push Token Management**: Store and manage iOS push tokens
4. **Call Routing**: Route calls between Android and iOS devices

## Implementation Details

### VoIP Call Flow
1. **Incoming Call**: Backend receives call from Android
2. **Push Notification**: Server sends VoIP push to iOS
3. **App Wake**: PushKit wakes iOS app
4. **CallKit Report**: App reports incoming call to CallKit
5. **User Answer**: User answers via native call interface
6. **SIP/WebRTC Setup**: Establish media connection
7. **Audio Stream**: Bidirectional audio between devices

### Key Classes

#### VoIPManager
- Handles SIP/WebRTC connections
- Manages call sessions
- Monitors network connectivity
- Implements retry mechanisms

#### PushKitManager
- Registers for VoIP push notifications
- Handles incoming push payloads
- Manages push token updates

#### CallKitManager
- Integrates with iOS CallKit framework
- Provides native call interface
- Handles call actions (answer, end, hold)

#### AudioManager
- Configures audio sessions for VoIP
- Manages audio routing (speaker, Bluetooth)
- Handles audio interruptions

## Testing

### 1. Simulator Testing
- Limited VoIP functionality in simulator
- Use device for full testing

### 2. Device Testing
```bash
# Install on device
# Enable VoIP service in app
# Test with your backend server
```

### 3. Push Notification Testing
- Use Apple's Push Notification Console
- Test VoIP push delivery
- Verify app wake behavior

## Troubleshooting

### Common Issues

1. **VoIP Push Not Received**:
   - Check push certificate
   - Verify bundle identifier
   - Ensure VoIP entitlement is enabled

2. **Audio Issues**:
   - Check microphone permissions
   - Verify audio session configuration
   - Test different audio routes

3. **Background Issues**:
   - Ensure VoIP background mode is enabled
   - Check background app refresh settings
   - Verify network connectivity

### Debug Logging
The app includes comprehensive logging. Check logs via:
- In-app logs viewer
- Xcode console
- Device logs in Console.app

## Security Considerations

1. **Secure Storage**: Use Keychain for sensitive data
2. **TLS/SRTP**: Enable encryption for SIP/WebRTC
3. **Token Security**: Protect push tokens
4. **Authentication**: Implement proper SIP authentication

## Performance Optimization

1. **Battery Usage**: Optimize background processing
2. **Network Usage**: Implement efficient keep-alive
3. **Memory Management**: Proper cleanup of call sessions
4. **Audio Quality**: Adaptive codec selection

## License

This project is provided as a reference implementation. Modify according to your needs and comply with relevant licenses for any third-party libraries used.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review Apple's VoIP documentation
3. Consult your chosen VoIP library documentation
4. Test with your specific backend implementation
