# Build Instructions - VoIP Call Forwarding System

## 🏗️ Overview

This guide provides comprehensive build instructions for all three components of the VoIP Call Forwarding System: Android app, iOS app, and Backend service.

## 📋 Prerequisites

### Development Environment
- **Operating System**: macOS (for iOS development), Windows/Linux/macOS (for Android/Backend)
- **Git**: Version control system
- **Node.js**: 18.0+ (for Backend)
- **Java**: JDK 11+ (for Android)
- **Xcode**: 14.0+ (for iOS, macOS only)

### Required Accounts
- **Apple Developer Account**: For iOS app signing and push notifications
- **Google Developer Account**: For Android app publishing (optional)
- **Cloud Provider Account**: For backend deployment (AWS/GCP/Azure)

## 🤖 Android App Build

### Environment Setup

#### 1. Install Android Studio
```bash
# Download Android Studio from https://developer.android.com/studio
# Install Android SDK API Level 21+ (Android 5.0)
# Install Android Build Tools 30.0.3+
```

#### 2. Configure SDK and Tools
```bash
# Set ANDROID_HOME environment variable
export ANDROID_HOME=$HOME/Android/Sdk
export PATH=$PATH:$ANDROID_HOME/emulator
export PATH=$PATH:$ANDROID_HOME/tools
export PATH=$PATH:$ANDROID_HOME/tools/bin
export PATH=$PATH:$ANDROID_HOME/platform-tools

# Install required SDK components
sdkmanager "platforms;android-30"
sdkmanager "build-tools;30.0.3"
sdkmanager "extras;android;m2repository"
sdkmanager "extras;google;m2repository"
```

### Project Configuration

#### 1. Clone and Setup Project
```bash
git clone <repository-url>
cd VOIP/Android
```

#### 2. Configure gradle.properties
```properties
# gradle.properties
android.useAndroidX=true
android.enableJetifier=true

# Backend Configuration
BACKEND_BASE_URL=https://your-backend-server.com
BACKEND_API_VERSION=v1

# Build Configuration
org.gradle.jvmargs=-Xmx2048m -Dfile.encoding=UTF-8
org.gradle.parallel=true
org.gradle.caching=true
```

#### 3. Configure local.properties
```properties
# local.properties
sdk.dir=/path/to/Android/Sdk

# Signing Configuration (for release builds)
RELEASE_STORE_FILE=../keystore/release.keystore
RELEASE_STORE_PASSWORD=your_keystore_password
RELEASE_KEY_ALIAS=your_key_alias
RELEASE_KEY_PASSWORD=your_key_password
```

### Dependencies Configuration

#### 1. Update app/build.gradle
```gradle
android {
    compileSdk 33
    
    defaultConfig {
        applicationId "com.voipforwarding.android"
        minSdk 21
        targetSdk 33
        versionCode 1
        versionName "1.0.0"
        
        buildConfigField "String", "BACKEND_URL", "\"${BACKEND_BASE_URL}\""
        buildConfigField "String", "API_VERSION", "\"${BACKEND_API_VERSION}\""
    }
    
    buildTypes {
        debug {
            debuggable true
            minifyEnabled false
            applicationIdSuffix ".debug"
        }
        
        release {
            minifyEnabled true
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.release
        }
    }
    
    signingConfigs {
        release {
            storeFile file(RELEASE_STORE_FILE)
            storePassword RELEASE_STORE_PASSWORD
            keyAlias RELEASE_KEY_ALIAS
            keyPassword RELEASE_KEY_PASSWORD
        }
    }
}

dependencies {
    // Core Android libraries
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.core:core-ktx:1.9.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.6.2'
    
    // Networking
    implementation 'com.squareup.retrofit2:retrofit:2.9.0'
    implementation 'com.squareup.retrofit2:converter-gson:2.9.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.10.0'
    
    // WebRTC
    implementation 'org.webrtc:google-webrtc:1.0.32006'
    
    // SIP Library
    implementation 'org.pjsip.pjsua2:pjsua2:2.13'
    
    // WebSocket
    implementation 'io.socket:socket.io-client:2.0.1'
    
    // Permissions
    implementation 'com.karumi:dexter:6.2.3'
    
    // Testing
    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}
```

### Build Process

#### 1. Debug Build
```bash
# Clean and build debug version
./gradlew clean
./gradlew assembleDebug

# Install on connected device
./gradlew installDebug

# Run tests
./gradlew testDebugUnitTest
./gradlew connectedDebugAndroidTest
```

#### 2. Release Build
```bash
# Generate signed APK
./gradlew assembleRelease

# Generate App Bundle (for Play Store)
./gradlew bundleRelease

# Output files location:
# APK: app/build/outputs/apk/release/app-release.apk
# AAB: app/build/outputs/bundle/release/app-release.aab
```

### Code Signing

#### 1. Generate Keystore
```bash
keytool -genkey -v -keystore release.keystore -alias voip_android -keyalg RSA -keysize 2048 -validity 10000
```

#### 2. Configure Signing
```gradle
// In app/build.gradle
android {
    signingConfigs {
        release {
            storeFile file('../keystore/release.keystore')
            storePassword 'your_keystore_password'
            keyAlias 'voip_android'
            keyPassword 'your_key_password'
        }
    }
}
```

## 🍎 iOS App Build

### Environment Setup

#### 1. Install Xcode and Tools
```bash
# Install Xcode from Mac App Store
# Install Xcode Command Line Tools
xcode-select --install

# Install CocoaPods
sudo gem install cocoapods
```

#### 2. Configure Apple Developer Account
1. Sign in to Xcode with your Apple Developer Account
2. Configure development team in Xcode preferences
3. Create App ID in Apple Developer Console
4. Generate VoIP Services Certificate

### Project Configuration

#### 1. Setup Project
```bash
cd VOIP/iOS/VoIPReceiver
pod install
open VoIPReceiver.xcworkspace
```

#### 2. Configure Podfile
```ruby
# Podfile
platform :ios, '13.0'
use_frameworks!

target 'VoIPReceiver' do
  # Core pods
  pod 'Alamofire', '~> 5.6'
  pod 'SwiftyJSON', '~> 5.0'
  pod 'SocketIO', '~> 16.0'
  
  # WebRTC
  pod 'GoogleWebRTC', '~> 1.1'
  
  # SIP (choose one)
  pod 'linphone-sdk', '~> 5.1'
  # OR
  pod 'PJSIP', '~> 2.13'
  
  # UI and utilities
  pod 'SnapKit', '~> 5.6'
  pod 'SwiftLint', '~> 0.50'
  
  target 'VoIPReceiverTests' do
    inherit! :search_paths
    pod 'Quick', '~> 6.0'
    pod 'Nimble', '~> 11.0'
  end
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '13.0'
    end
  end
end
```

#### 3. Configure Info.plist
```xml
<!-- Info.plist -->
<dict>
    <!-- App Information -->
    <key>CFBundleDisplayName</key>
    <string>VoIP Receiver</string>
    <key>CFBundleIdentifier</key>
    <string>com.yourcompany.voipreceiver</string>
    <key>CFBundleVersion</key>
    <string>1.0.0</string>
    
    <!-- VoIP Configuration -->
    <key>UIBackgroundModes</key>
    <array>
        <string>voip</string>
        <string>audio</string>
        <string>background-processing</string>
    </array>
    
    <!-- Permissions -->
    <key>NSMicrophoneUsageDescription</key>
    <string>This app needs microphone access for VoIP calls</string>
    
    <!-- Required Device Capabilities -->
    <key>UIRequiredDeviceCapabilities</key>
    <array>
        <string>armv7</string>
        <string>microphone</string>
    </array>
    
    <!-- Network Security -->
    <key>NSAppTransportSecurity</key>
    <dict>
        <key>NSAllowsArbitraryLoads</key>
        <false/>
        <key>NSExceptionDomains</key>
        <dict>
            <key>your-backend-server.com</key>
            <dict>
                <key>NSExceptionAllowsInsecureHTTPLoads</key>
                <true/>
                <key>NSExceptionMinimumTLSVersion</key>
                <string>TLSv1.0</string>
            </dict>
        </dict>
    </dict>
</dict>
```

### Apple Push Notification Setup

#### 1. Create VoIP Services Certificate
1. Go to Apple Developer Console
2. Navigate to Certificates, Identifiers & Profiles
3. Create new Key for Apple Push Notifications service (APNs)
4. Download the .p8 key file
5. Note the Key ID and Team ID

#### 2. Configure Push Notifications
```swift
// In AppDelegate.swift
import PushKit

class AppDelegate: UIResponder, UIApplicationDelegate {
    var voipRegistry: PKPushRegistry!
    
    func application(_ application: UIApplication, 
                    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        
        // Configure VoIP push notifications
        voipRegistry = PKPushRegistry(queue: DispatchQueue.main)
        voipRegistry.delegate = self
        voipRegistry.desiredPushTypes = [.voIP]
        
        return true
    }
}

extension AppDelegate: PKPushRegistryDelegate {
    func pushRegistry(_ registry: PKPushRegistry, didUpdate pushCredentials: PKPushCredentials, for type: PKPushType) {
        let token = pushCredentials.token.map { String(format: "%02x", $0) }.joined()
        // Send token to backend server
        VoIPManager.shared.registerPushToken(token)
    }
    
    func pushRegistry(_ registry: PKPushRegistry, didReceiveIncomingPushWith payload: PKPushPayload, for type: PKPushType) {
        // Handle incoming VoIP push
        VoIPManager.shared.handleIncomingCall(payload: payload.dictionaryPayload)
    }
}
```

### Build Process

#### 1. Development Build
```bash
# Build for simulator
xcodebuild -workspace VoIPReceiver.xcworkspace \
           -scheme VoIPReceiver \
           -destination 'platform=iOS Simulator,name=iPhone 14' \
           build

# Build for device
xcodebuild -workspace VoIPReceiver.xcworkspace \
           -scheme VoIPReceiver \
           -destination 'platform=iOS,name=Your Device' \
           build
```

#### 2. Archive for Distribution
```bash
# Create archive
xcodebuild -workspace VoIPReceiver.xcworkspace \
           -scheme VoIPReceiver \
           -archivePath VoIPReceiver.xcarchive \
           archive

# Export IPA
xcodebuild -exportArchive \
           -archivePath VoIPReceiver.xcarchive \
           -exportPath ./build \
           -exportOptionsPlist ExportOptions.plist
```

#### 3. ExportOptions.plist
```xml
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <key>method</key>
    <string>app-store</string>
    <key>teamID</key>
    <string>YOUR_TEAM_ID</string>
    <key>uploadBitcode</key>
    <false/>
    <key>uploadSymbols</key>
    <true/>
    <key>compileBitcode</key>
    <false/>
</dict>
</plist>
```

### Code Signing and Provisioning

#### 1. Configure Signing
1. In Xcode, select the project
2. Go to Signing & Capabilities
3. Select your development team
4. Enable "Automatically manage signing" or configure manual signing
5. Add VoIP background mode capability

#### 2. Provisioning Profiles
```bash
# Download provisioning profiles
# Development profile for testing
# Distribution profile for App Store

# Install profiles
open YourApp_Development.mobileprovision
open YourApp_Distribution.mobileprovision
```

## 🖥️ Backend Service Build

### Environment Setup

#### 1. Install Node.js and Dependencies
```bash
# Install Node.js 18+
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install global dependencies
npm install -g @nestjs/cli typescript ts-node

# Verify installation
node --version
npm --version
```

#### 2. Database Setup
```bash
# Install PostgreSQL
sudo apt-get install postgresql postgresql-contrib

# Create database and user
sudo -u postgres psql
CREATE DATABASE voip_db;
CREATE USER voip_user WITH ENCRYPTED PASSWORD 'secure_password';
GRANT ALL PRIVILEGES ON DATABASE voip_db TO voip_user;
\q
```

### Project Configuration

#### 1. Install Dependencies
```bash
cd VOIP/Backend
npm install
```

#### 2. Environment Configuration
```bash
# Copy environment template
cp .env.example .env

# Configure environment variables
cat > .env << EOF
# Application
NODE_ENV=production
PORT=3000

# Database
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=voip_user
DB_PASSWORD=secure_password
DB_DATABASE=voip_db

# JWT
JWT_SECRET=your-super-secure-jwt-secret-key
JWT_REFRESH_SECRET=your-refresh-secret-key

# Apple Push Notifications
APN_KEY_PATH=/app/apn-keys/AuthKey_XXXXXXXXXX.p8
APN_KEY_ID=XXXXXXXXXX
APN_TEAM_ID=XXXXXXXXXX
IOS_BUNDLE_ID=com.yourcompany.voipreceiver

# WebRTC
STUN_SERVERS=stun:stun.l.google.com:19302
TURN_SERVERS=turn:your-turn-server.com:3478
TURN_USERNAME=your-turn-username
TURN_PASSWORD=your-turn-password
EOF
```

### Build Process

#### 1. Development Build
```bash
# Install dependencies
npm install

# Run database migrations
npm run migration:run

# Start development server
npm run start:dev

# Run tests
npm run test
npm run test:e2e
```

#### 2. Production Build
```bash
# Build for production
npm run build

# Start production server
npm run start:prod

# Or use PM2 for process management
npm install -g pm2
pm2 start dist/main.js --name voip-backend
```

### Docker Build

#### 1. Build Docker Image
```bash
# Build image
docker build -t voip-backend:latest .

# Tag for registry
docker tag voip-backend:latest your-registry/voip-backend:1.0.0
```

#### 2. Docker Compose Build
```bash
# Build and start all services
docker-compose up --build -d

# View logs
docker-compose logs -f voip-backend

# Stop services
docker-compose down
```

### Database Migrations

#### 1. Generate Migration
```bash
# Generate new migration
npm run migration:generate -- -n CreateInitialTables

# Run migrations
npm run migration:run

# Revert migration
npm run migration:revert
```

## 🚀 Deployment Builds

### Android App Store Deployment

#### 1. Prepare Release Build
```bash
# Generate signed APK/AAB
./gradlew bundleRelease

# Test on device
./gradlew installRelease
```

#### 2. Google Play Console Upload
1. Create app listing in Google Play Console
2. Upload AAB file
3. Configure app details and screenshots
4. Submit for review

### iOS App Store Deployment

#### 1. Archive and Upload
```bash
# Archive for distribution
xcodebuild -workspace VoIPReceiver.xcworkspace \
           -scheme VoIPReceiver \
           -archivePath VoIPReceiver.xcarchive \
           archive

# Upload to App Store Connect
xcodebuild -exportArchive \
           -archivePath VoIPReceiver.xcarchive \
           -exportPath ./build \
           -exportOptionsPlist ExportOptions.plist
```

#### 2. App Store Connect
1. Upload IPA using Xcode or Application Loader
2. Configure app metadata
3. Add screenshots and descriptions
4. Submit for App Store review

### Backend Production Deployment

#### 1. Cloud Deployment (AWS)
```bash
# Build and push to ECR
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 123456789012.dkr.ecr.us-east-1.amazonaws.com
docker build -t voip-backend .
docker tag voip-backend:latest 123456789012.dkr.ecr.us-east-1.amazonaws.com/voip-backend:latest
docker push 123456789012.dkr.ecr.us-east-1.amazonaws.com/voip-backend:latest

# Deploy to ECS
aws ecs update-service --cluster voip-cluster --service voip-backend-service --force-new-deployment
```

#### 2. Kubernetes Deployment
```bash
# Build and push image
docker build -t voip-backend:1.0.0 .
docker push your-registry/voip-backend:1.0.0

# Deploy to Kubernetes
kubectl apply -f k8s/
kubectl rollout status deployment/voip-backend
```

## ✅ Build Verification

### Testing Checklist

#### Android App
- [ ] App installs and launches successfully
- [ ] Permissions are requested and granted
- [ ] Backend connection is established
- [ ] Device registration works
- [ ] Call detection functions properly
- [ ] WebRTC connection establishes
- [ ] Audio quality is acceptable

#### iOS App
- [ ] App installs and launches successfully
- [ ] VoIP push notifications are received
- [ ] CallKit integration works
- [ ] Background mode functions
- [ ] Audio session management works
- [ ] Device pairing is successful

#### Backend Service
- [ ] Service starts without errors
- [ ] Database connections are established
- [ ] API endpoints respond correctly
- [ ] WebSocket connections work
- [ ] Push notifications are sent
- [ ] Authentication functions properly

### Performance Testing
- [ ] Load testing with multiple concurrent calls
- [ ] Memory usage monitoring
- [ ] Network latency measurements
- [ ] Battery usage optimization
- [ ] Call quality metrics validation

---

**Build completed successfully?** Proceed to the [Integration Guide](INTEGRATION_GUIDE.md) to configure all components to work together.
