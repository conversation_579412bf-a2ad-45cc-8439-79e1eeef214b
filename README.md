# VoIP Call Forwarding System

A comprehensive VoIP solution that enables call forwarding from Android devices to iOS devices over the internet, eliminating the need for SIM cards in iOS devices. The system consists of three main components working together to provide seamless call forwarding functionality.

## 🏗️ System Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Android App   │    │  NestJS Backend │    │    iOS App      │
│                 │    │                 │    │                 │
│ • Call Detection│◄──►│ • Device Mgmt   │◄──►│ • VoIP Receiver │
│ • SIP Client    │    │ • Call Routing  │    │ • CallKit UI    │
│ • Push Sender   │    │ • Push Service  │    │ • PushKit Wake  │
│ • WebRTC        │    │ • WebRTC Bridge │    │ • Audio Session │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────▼─────────────┐
                    │      PostgreSQL DB       │
                    │   • Device Registry      │
                    │   • Call Logs           │
                    │   • Push Notifications  │
                    └─────────────────────────┘
```

## 📱 Components Overview

### 1. **Android App** (`/Android`)
- **Purpose**: Detects incoming calls and forwards them to paired iOS device
- **Key Features**: Call interception, SIP client, push notification sending
- **Technology**: Java/Kotlin, Android SDK, SIP library

### 2. **iOS App** (`/iOS`)
- **Purpose**: Receives forwarded calls and provides native iOS calling experience
- **Key Features**: VoIP push notifications, CallKit integration, background operation
- **Technology**: Swift, CallKit, PushKit, AVAudioSession

### 3. **Backend Service** (`/Backend`)
- **Purpose**: Manages devices, routes calls, handles push notifications
- **Key Features**: Device pairing, call management, real-time communication
- **Technology**: NestJS, TypeScript, PostgreSQL, WebSocket, Apple Push Notifications

## 🚀 Quick Start

### Prerequisites
- **Android Development**: Android Studio, Android SDK 21+
- **iOS Development**: Xcode 14+, iOS 13+, Apple Developer Account
- **Backend**: Node.js 18+, PostgreSQL 12+, Docker (optional)

### 1. Backend Setup
```bash
cd Backend
cp .env.example .env
# Configure environment variables
npm install
docker-compose up -d
```

### 2. iOS App Setup
```bash
cd iOS/VoIPReceiver
pod install
# Configure Apple Push Notification certificates
# Open VoIPReceiver.xcworkspace in Xcode
```

### 3. Android App Setup
```bash
cd Android
# Configure gradle.properties with backend URL
# Open project in Android Studio
```

## 📚 Documentation

### 📖 **Technical Documentation**
- **[TECHNICAL_OVERVIEW.md](TECHNICAL_OVERVIEW.md)** - System architecture, API specs, data flow
- **[API_REFERENCE.md](API_REFERENCE.md)** - Complete API documentation with examples
- **[DATABASE_SCHEMA.md](DATABASE_SCHEMA.md)** - Database design and relationships

### 👥 **User Documentation**
- **[USER_GUIDE.md](USER_GUIDE.md)** - End-user setup and usage instructions
- **[TROUBLESHOOTING.md](TROUBLESHOOTING.md)** - Common issues and solutions
- **[FEATURE_GUIDE.md](FEATURE_GUIDE.md)** - Detailed feature explanations

### 🔧 **Development & Deployment**
- **[BUILD_INSTRUCTIONS.md](BUILD_INSTRUCTIONS.md)** - Build guides for all components
- **[DEPLOYMENT_GUIDE.md](DEPLOYMENT_GUIDE.md)** - Production deployment instructions
- **[INTEGRATION_GUIDE.md](INTEGRATION_GUIDE.md)** - Component integration and configuration

### 🔒 **Security & Configuration**
- **[SECURITY_GUIDE.md](SECURITY_GUIDE.md)** - Security implementation and best practices
- **[CONFIGURATION_GUIDE.md](CONFIGURATION_GUIDE.md)** - Environment and configuration setup

## ✨ Key Features

### 🔄 **Call Forwarding**
- Automatic detection of incoming calls on Android
- Real-time forwarding to paired iOS device
- Native iOS calling experience with CallKit
- Background operation support

### 📱 **Device Management**
- Easy device pairing process
- Multi-device support
- Device status monitoring
- Automatic reconnection

### 🔔 **Push Notifications**
- VoIP push notifications for instant call wake
- Background app activation
- Reliable delivery with retry logic
- Custom notification payloads

### 📊 **Call Management**
- Complete call history
- Call quality monitoring
- Real-time call statistics
- Call recording support (optional)

### 🌐 **Real-time Communication**
- WebRTC peer-to-peer connections
- SIP protocol support
- Low-latency signaling
- NAT traversal with STUN/TURN

## 🛠️ Development Workflow

### 1. **Environment Setup**
```bash
# Clone repository
git clone <repository-url>
cd VOIP

# Setup backend
cd Backend && npm install && docker-compose up -d

# Setup iOS app
cd ../iOS/VoIPReceiver && pod install

# Setup Android app
cd ../../Android
# Open in Android Studio
```

### 2. **Configuration**
- Configure backend environment variables
- Setup Apple Push Notification certificates
- Configure Android app with backend URL
- Setup device pairing

### 3. **Testing**
- Unit tests for each component
- Integration testing between components
- End-to-end call flow testing
- Performance and load testing

## 📋 System Requirements

### **Backend Service**
- **Runtime**: Node.js 18+
- **Database**: PostgreSQL 12+
- **Memory**: 512MB minimum, 1GB recommended
- **Storage**: 10GB minimum for logs and database

### **iOS App**
- **iOS Version**: 13.0+
- **Xcode**: 14.0+
- **Device**: iPhone (iPad support optional)
- **Certificates**: VoIP Services Certificate required

### **Android App**
- **Android Version**: API 21+ (Android 5.0)
- **Android Studio**: 2022.1.1+
- **Permissions**: Phone, Microphone, Network access
- **Google Services**: Firebase Cloud Messaging

## 🔐 Security Features

- **End-to-End Encryption**: WebRTC SRTP encryption
- **JWT Authentication**: Secure API access with refresh tokens
- **Certificate Pinning**: SSL/TLS certificate validation
- **Input Validation**: Comprehensive request validation
- **Rate Limiting**: API abuse protection
- **Audit Logging**: Complete activity logging

## 📈 Performance Metrics

- **Call Setup Time**: < 3 seconds
- **Audio Latency**: < 150ms
- **Push Notification Delivery**: < 2 seconds
- **API Response Time**: < 100ms
- **System Uptime**: 99.9% target

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Commit changes**: `git commit -m 'Add amazing feature'`
4. **Push to branch**: `git push origin feature/amazing-feature`
5. **Open a Pull Request**

### Development Guidelines
- Follow existing code style and conventions
- Add unit tests for new features
- Update documentation for API changes
- Test on both Android and iOS devices

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

### **Getting Help**
- **Documentation**: Check the comprehensive guides above
- **Issues**: Create GitHub issues for bugs and feature requests
- **Discussions**: Use GitHub Discussions for questions
- **Email**: Contact the development team

### **Common Issues**
- **Call Quality**: Check network connectivity and STUN/TURN configuration
- **Push Notifications**: Verify Apple certificates and device tokens
- **Device Pairing**: Ensure both devices are connected to the backend
- **Audio Issues**: Check microphone permissions and audio session configuration

## 🗺️ Roadmap

### **Phase 1** ✅ (Completed)
- Basic call forwarding functionality
- Device pairing system
- Push notification integration
- Core backend services

### **Phase 2** 🚧 (In Progress)
- Video call support
- Group calling features
- Advanced call quality monitoring
- Web-based management interface

### **Phase 3** 📋 (Planned)
- Call recording and playback
- AI-powered call analytics
- Multi-platform support (Windows, macOS)
- Enterprise features and SSO integration

---

**Built with ❤️ by the VoIP Development Team**

For detailed information about each component, please refer to the specific documentation files linked above.
