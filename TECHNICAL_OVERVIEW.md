# Technical Overview - VoIP Call Forwarding System

## 🏗️ System Architecture

### High-Level Architecture

```
┌─────────────────────────────────────────────────────────────────────────────┐
│                           VoIP Call Forwarding System                        │
├─────────────────┬─────────────────────────┬─────────────────────────────────┤
│   Android App   │     Backend Service     │          iOS App                │
│                 │                         │                                 │
│ ┌─────────────┐ │ ┌─────────────────────┐ │ ┌─────────────────────────────┐ │
│ │Call Detector│ │ │   Device Manager    │ │ │      PushKit Manager        │ │
│ │             │ │ │                     │ │ │                             │ │
│ │• PhoneState │ │ │• Registration       │ │ │• VoIP Push Reception        │ │
│ │• TelecomMgr │ │ │• Pairing            │ │ │• Background App Wake        │ │
│ │• Broadcast  │ │ │• Status Tracking    │ │ │• Token Management           │ │
│ └─────────────┘ │ └─────────────────────┘ │ └─────────────────────────────┘ │
│                 │                         │                                 │
│ ┌─────────────┐ │ ┌─────────────────────┐ │ ┌─────────────────────────────┐ │
│ │ SIP Client  │ │ │   Call Manager      │ │ │     CallKit Manager         │ │
│ │             │ │ │                     │ │ │                             │ │
│ │• PJSIP      │ │ │• Call Routing       │ │ │• Native Call Interface      │ │
│ │• WebRTC     │ │ │• Session Mgmt       │ │ │• Call Actions (Answer/End)  │ │
│ │• Audio      │ │ │• Quality Monitor    │ │ │• Audio Route Control        │ │
│ └─────────────┘ │ └─────────────────────┘ │ └─────────────────────────────┘ │
│                 │                         │                                 │
│ ┌─────────────┐ │ ┌─────────────────────┐ │ ┌─────────────────────────────┐ │
│ │Push Sender  │ │ │  Push Service       │ │ │      VoIP Manager           │ │
│ │             │ │ │                     │ │ │                             │ │
│ │• FCM Client │ │ │• Apple Push (APN)   │ │ │• SIP/WebRTC Client          │ │
│ │• Trigger    │ │ │• Notification Queue │ │ │• Media Session              │ │
│ │• Metadata   │ │ │• Retry Logic        │ │ │• Connection Management      │ │
│ └─────────────┘ │ └─────────────────────┘ │ └─────────────────────────────┘ │
│                 │                         │                                 │
│ ┌─────────────┐ │ ┌─────────────────────┐ │ ┌─────────────────────────────┐ │
│ │WebSocket    │ │ │  WebSocket Gateway  │ │ │     WebSocket Client        │ │
│ │Client       │ │ │                     │ │ │                             │ │
│ │             │ │ │• Real-time Signaling│ │ │• Signaling Reception        │ │
│ │• Signaling  │ │ │• WebRTC Coordination│ │ │• Call State Updates         │ │
│ │• Call Events│ │ │• Connection Mgmt    │ │ │• Quality Reporting          │ │
│ └─────────────┘ │ └─────────────────────┘ │ └─────────────────────────────┘ │
└─────────────────┴─────────────────────────┴─────────────────────────────────┘
                                │
                   ┌─────────────▼─────────────┐
                   │      Data Layer           │
                   │                           │
                   │ ┌─────────────────────────┐ │
                   │ │    PostgreSQL DB        │ │
                   │ │                         │ │
                   │ │• devices                │ │
                   │ │• call_logs              │ │
                   │ │• push_notifications     │ │
                   │ │• user_sessions          │ │
                   │ └─────────────────────────┘ │
                   │                           │
                   │ ┌─────────────────────────┐ │
                   │ │      Redis Cache        │ │
                   │ │                         │ │
                   │ │• Session Storage        │ │
                   │ │• Rate Limiting          │ │
                   │ │• Temporary Data         │ │
                   │ └─────────────────────────┘ │
                   └───────────────────────────┘
```

## 🔄 Call Flow Process

### 1. System Initialization

```mermaid
sequenceDiagram
    participant A as Android App
    participant B as Backend Service
    participant I as iOS App
    participant D as Database
    participant P as Push Service

    Note over A,P: Device Registration & Pairing
    
    A->>B: POST /devices/register (Android device info)
    B->>D: Store device record
    B-->>A: Device registered
    
    I->>B: POST /devices/register (iOS device info + push token)
    B->>D: Store device record
    B-->>I: Device registered
    
    A->>B: POST /devices/pair (Android + iOS device IDs)
    B->>D: Create device pairing
    B->>P: Send pairing notification to iOS
    P->>I: Push notification (device paired)
    B-->>A: Devices paired successfully
```

### 2. Incoming Call Detection & Forwarding

```mermaid
sequenceDiagram
    participant C as Caller
    participant A as Android App
    participant B as Backend Service
    participant P as Push Service
    participant I as iOS App
    participant W as WebSocket

    Note over C,W: Call Forwarding Flow
    
    C->>A: Incoming call detected
    A->>A: Extract caller info (number, name)
    A->>B: POST /calls/initiate (caller info)
    B->>B: Create call session
    B->>P: Send VoIP push to iOS
    P->>I: VoIP push notification
    I->>I: Wake app, show CallKit UI
    
    Note over A,I: Real-time Signaling
    A->>W: Connect WebSocket
    I->>W: Connect WebSocket
    A->>W: WebRTC offer (SDP)
    W->>I: Forward offer
    I->>W: WebRTC answer (SDP)
    W->>A: Forward answer
    
    Note over A,I: ICE Candidate Exchange
    A->>W: ICE candidates
    W->>I: Forward ICE candidates
    I->>W: ICE candidates
    W->>A: Forward ICE candidates
    
    Note over A,I: Call Established
    A<-->I: Direct WebRTC connection
    I->>B: PUT /calls/{id} (call answered)
    B->>B: Update call status
```

## 📡 API Specifications

### Authentication Flow

```http
POST /api/v1/auth/authenticate
Content-Type: application/json

{
  "deviceId": "android-device-123",
  "sipUsername": "user123",
  "sipPassword": "secure-password"
}

Response:
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "expires_in": 86400,
  "device": {
    "id": "uuid",
    "deviceId": "android-device-123",
    "deviceType": "android",
    "status": "active"
  }
}
```

### Device Management APIs

```http
# Register Device
POST /api/v1/devices/register
Authorization: Bearer {token}
Content-Type: application/json

{
  "deviceId": "unique-device-id",
  "deviceType": "android|ios",
  "deviceName": "Device Name",
  "osVersion": "OS Version",
  "appVersion": "App Version",
  "pushToken": "push-notification-token",
  "sipUsername": "sip-username",
  "sipPassword": "sip-password",
  "capabilities": {
    "supportsVideo": false,
    "supportedCodecs": ["PCMU", "PCMA", "opus"],
    "maxBitrate": 64000
  }
}

# Pair Devices
POST /api/v1/devices/pair
Authorization: Bearer {token}
Content-Type: application/json

{
  "androidDeviceId": "android-device-123",
  "iosDeviceId": "ios-device-456"
}
```

### Call Management APIs

```http
# Initiate Call
POST /api/v1/calls/initiate
Authorization: Bearer {token}
Content-Type: application/json

{
  "androidDeviceId": "android-device-123",
  "callerId": "+**********",
  "callerName": "John Doe"
}

# Update Call Status
PUT /api/v1/calls/{callId}
Authorization: Bearer {token}
Content-Type: application/json

{
  "status": "answered|connected|ended",
  "qualityMetrics": {
    "jitter": 12.5,
    "packetLoss": 0.1,
    "roundTripTime": 45,
    "audioLevel": -20,
    "qualityScore": "good"
  }
}
```

## 🗄️ Database Schema

### Core Entities

```sql
-- Devices table
CREATE TABLE devices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    device_id VARCHAR(255) UNIQUE NOT NULL,
    device_type device_type_enum NOT NULL,
    device_name VARCHAR(255),
    os_version VARCHAR(100),
    app_version VARCHAR(100),
    push_token TEXT,
    sip_username VARCHAR(255),
    sip_password VARCHAR(255), -- encrypted
    sip_domain VARCHAR(255),
    status device_status_enum DEFAULT 'active',
    capabilities JSONB,
    network_info JSONB,
    paired_device_id UUID REFERENCES devices(id),
    last_activity TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Call logs table
CREATE TABLE call_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    call_id VARCHAR(255) UNIQUE NOT NULL,
    initiator_device_id UUID REFERENCES devices(id),
    receiver_device_id UUID REFERENCES devices(id),
    caller_id VARCHAR(50) NOT NULL,
    caller_name VARCHAR(255),
    direction call_direction_enum NOT NULL,
    status call_status_enum DEFAULT 'initiated',
    start_time TIMESTAMP NOT NULL,
    answer_time TIMESTAMP,
    end_time TIMESTAMP,
    duration INTEGER DEFAULT 0,
    end_reason call_end_reason_enum,
    codec VARCHAR(50),
    quality_metrics JSONB,
    network_stats JSONB,
    sip_details JSONB,
    error_info JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Push notifications table
CREATE TABLE push_notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    device_id UUID REFERENCES devices(id),
    type push_notification_type_enum NOT NULL,
    title VARCHAR(255) NOT NULL,
    body TEXT NOT NULL,
    payload JSONB NOT NULL,
    status push_notification_status_enum DEFAULT 'pending',
    apns_id VARCHAR(255),
    priority INTEGER DEFAULT 10,
    expires_at TIMESTAMP,
    sent_at TIMESTAMP,
    delivered_at TIMESTAMP,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    error_info JSONB,
    response_time INTEGER,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### Entity Relationships

```
devices (1) ←→ (1) devices [paired_device_id]
devices (1) ←→ (∞) call_logs [initiator_device_id]
devices (1) ←→ (∞) call_logs [receiver_device_id]
devices (1) ←→ (∞) push_notifications [device_id]
```

## 🔐 Security Implementation

### JWT Authentication

```typescript
// JWT Payload Structure
interface JwtPayload {
  sub: string;        // device ID
  deviceId: string;   // device identifier
  deviceType: string; // android|ios
  iat: number;        // issued at
  exp: number;        // expiration
}

// Token Generation
const payload: JwtPayload = {
  sub: device.id,
  deviceId: device.deviceId,
  deviceType: device.deviceType,
};

const accessToken = jwt.sign(payload, JWT_SECRET, { expiresIn: '24h' });
const refreshToken = jwt.sign(payload, JWT_REFRESH_SECRET, { expiresIn: '7d' });
```

### Encryption & Security

```typescript
// Password Hashing
const saltRounds = 10;
const hashedPassword = await bcrypt.hash(password, saltRounds);

// SIP Password Verification
const isValid = await bcrypt.compare(password, device.sipPassword);

// API Rate Limiting
@Throttle(100, 60) // 100 requests per minute
@Controller('api/v1')
export class ApiController {
  // API endpoints
}
```

## 📱 WebRTC Signaling Process

### Signaling Flow

```javascript
// Android App - Initiating WebRTC Connection
const peerConnection = new RTCPeerConnection({
  iceServers: [
    { urls: 'stun:stun.l.google.com:19302' },
    { 
      urls: 'turn:turn-server.com:3478',
      username: 'user',
      credential: 'pass'
    }
  ]
});

// Create offer
const offer = await peerConnection.createOffer();
await peerConnection.setLocalDescription(offer);

// Send offer via WebSocket
socket.emit('webrtc-signal', {
  type: 'offer',
  callId: callId,
  fromDevice: 'android-device-123',
  toDevice: 'ios-device-456',
  data: { sdp: offer.sdp }
});

// iOS App - Handling WebRTC Offer
socket.on('webrtc-signal', async (message) => {
  if (message.type === 'offer') {
    await peerConnection.setRemoteDescription(message.data);
    
    const answer = await peerConnection.createAnswer();
    await peerConnection.setLocalDescription(answer);
    
    socket.emit('webrtc-signal', {
      type: 'answer',
      callId: message.callId,
      fromDevice: 'ios-device-456',
      toDevice: message.fromDevice,
      data: { sdp: answer.sdp }
    });
  }
});
```

## 🔔 Push Notification Workflow

### Apple Push Notification Service (APNs)

```typescript
// VoIP Push Notification Payload
const voipPayload = {
  caller_id: '+**********',
  caller_name: 'John Doe',
  call_uuid: 'unique-call-uuid',
  server_info: {
    sip_server: 'voip.example.com',
    port: 5060,
    transport: 'UDP'
  },
  timestamp: Date.now()
};

// Send VoIP Push
const notification = new apn.Notification();
notification.topic = 'com.yourapp.voipreceiver.voip';
notification.pushType = 'voip';
notification.priority = 10;
notification.payload = voipPayload;

const result = await apnProvider.send(notification, deviceToken);
```

### iOS Push Handling

```swift
// PushKit Delegate
func pushRegistry(_ registry: PKPushRegistry, 
                 didReceiveIncomingPushWith payload: PKPushPayload, 
                 for type: PKPushType) {
    guard type == .voIP else { return }
    
    let callerId = payload.dictionaryPayload["caller_id"] as? String ?? "Unknown"
    let callUUID = UUID(uuidString: payload.dictionaryPayload["call_uuid"] as? String ?? "") ?? UUID()
    
    // Report to CallKit
    let update = CXCallUpdate()
    update.remoteHandle = CXHandle(type: .phoneNumber, value: callerId)
    update.hasVideo = false
    
    callKitProvider.reportNewIncomingCall(with: callUUID, update: update) { error in
        if let error = error {
            print("Failed to report call: \(error)")
        }
    }
}
```

## 🎵 Audio Session Management

### iOS Audio Configuration

```swift
// Configure audio session for VoIP
let audioSession = AVAudioSession.sharedInstance()

try audioSession.setCategory(.playAndRecord, 
                           mode: .voiceChat, 
                           options: [.allowBluetooth, .allowBluetoothA2DP])
try audioSession.setActive(true)

// Handle audio route changes
NotificationCenter.default.addObserver(
    forName: AVAudioSession.routeChangeNotification,
    object: nil,
    queue: .main
) { notification in
    // Handle route change
}
```

### Android Audio Configuration

```java
// Configure audio manager for VoIP
AudioManager audioManager = (AudioManager) getSystemService(Context.AUDIO_SERVICE);
audioManager.setMode(AudioManager.MODE_IN_COMMUNICATION);
audioManager.setSpeakerphoneOn(false);

// Handle audio focus
AudioManager.OnAudioFocusChangeListener focusChangeListener = 
    new AudioManager.OnAudioFocusChangeListener() {
    @Override
    public void onAudioFocusChange(int focusChange) {
        // Handle focus change
    }
};

int result = audioManager.requestAudioFocus(focusChangeListener,
    AudioManager.STREAM_VOICE_CALL,
    AudioManager.AUDIOFOCUS_GAIN);
```

## 📊 Performance Monitoring

### Key Metrics

```typescript
// Call Quality Metrics
interface CallQualityMetrics {
  jitter: number;           // ms
  packetLoss: number;       // percentage
  roundTripTime: number;    // ms
  audioLevel: number;       // dB
  qualityScore: 'good' | 'fair' | 'poor';
}

// Network Statistics
interface NetworkStats {
  bytesReceived: number;
  bytesSent: number;
  packetsReceived: number;
  packetsSent: number;
  packetsLost: number;
}

// System Performance
interface SystemMetrics {
  cpuUsage: number;         // percentage
  memoryUsage: number;      // MB
  networkLatency: number;   // ms
  batteryLevel: number;     // percentage (mobile)
}
```

This technical overview provides the foundation for understanding how all three components work together to deliver seamless VoIP call forwarding functionality.
