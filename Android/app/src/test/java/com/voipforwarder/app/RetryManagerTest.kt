package com.voipforwarder.app

import com.voipforwarder.app.utils.RetryConfig
import com.voipforwarder.app.utils.RetryManager
import com.voipforwarder.app.utils.RetryResult
import kotlinx.coroutines.test.runTest
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import java.io.IOException
import java.net.SocketException

/**
 * Unit tests for RetryManager
 */
class RetryManagerTest {

    private lateinit var retryManager: RetryManager

    @Before
    fun setup() {
        retryManager = RetryManager()
    }

    @Test
    fun `executeWithRetry succeeds on first attempt`() = runTest {
        val result = retryManager.executeWithRetry(
            operation = { "success" },
            config = RetryConfig(maxRetries = 3)
        )

        assertTrue(result.isSuccess())
        assertEquals("success", result.getOrNull())
        assertEquals(0, (result as RetryResult.Success).attemptCount)
    }

    @Test
    fun `executeWithRetry succeeds after retries`() = runTest {
        var attemptCount = 0
        val result = retryManager.executeWithRetry(
            operation = {
                attemptCount++
                if (attemptCount < 3) {
                    throw IOException("Network error")
                } else {
                    "success"
                }
            },
            config = RetryConfig(maxRetries = 3, initialDelayMs = 10)
        )

        assertTrue(result.isSuccess())
        assertEquals("success", result.getOrNull())
        assertEquals(2, (result as RetryResult.Success).attemptCount)
    }

    @Test
    fun `executeWithRetry fails after max retries`() = runTest {
        val result = retryManager.executeWithRetry(
            operation = { throw IOException("Network error") },
            config = RetryConfig(maxRetries = 2, initialDelayMs = 10)
        )

        assertTrue(result.isFailure())
        assertEquals(2, (result as RetryResult.Failure).attemptCount)
        assertTrue(result.exception is IOException)
    }

    @Test
    fun `executeWithRetryIf respects retry condition`() = runTest {
        val result = retryManager.executeWithRetryIf(
            operation = { throw SecurityException("Auth error") },
            shouldRetry = { it is IOException }, // Only retry IOException
            config = RetryConfig(maxRetries = 3, initialDelayMs = 10)
        )

        assertTrue(result.isFailure())
        assertEquals(0, (result as RetryResult.Failure).attemptCount) // No retries
        assertTrue(result.exception is SecurityException)
    }

    @Test
    fun `executeWithRetryIf retries on matching condition`() = runTest {
        var attemptCount = 0
        val result = retryManager.executeWithRetryIf(
            operation = {
                attemptCount++
                if (attemptCount < 2) {
                    throw SocketException("Connection error")
                } else {
                    "success"
                }
            },
            shouldRetry = { it is SocketException },
            config = RetryConfig(maxRetries = 3, initialDelayMs = 10)
        )

        assertTrue(result.isSuccess())
        assertEquals("success", result.getOrNull())
        assertEquals(1, (result as RetryResult.Success).attemptCount)
    }
}
