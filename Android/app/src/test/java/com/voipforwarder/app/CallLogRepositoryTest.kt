package com.voipforwarder.app

import androidx.room.Room
import androidx.test.core.app.ApplicationProvider
import androidx.test.ext.junit.runners.AndroidJUnit4
import com.voipforwarder.app.data.database.CallLogDatabase
import com.voipforwarder.app.data.model.CallDirection
import com.voipforwarder.app.data.model.CallEvent
import com.voipforwarder.app.data.model.CallState
import com.voipforwarder.app.data.repository.CallLogRepository
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.test.runTest
import org.junit.After
import org.junit.Assert.*
import org.junit.Before
import org.junit.Test
import org.junit.runner.RunWith
import java.util.Date

/**
 * Integration tests for CallLogRepository
 */
@RunWith(AndroidJUnit4::class)
class CallLogRepositoryTest {

    private lateinit var database: CallLogDatabase
    private lateinit var repository: CallLogRepository

    @Before
    fun setup() {
        database = Room.inMemoryDatabaseBuilder(
            ApplicationProvider.getApplicationContext(),
            CallLogDatabase::class.java
        ).allowMainThreadQueries().build()

        repository = CallLogRepository(database.callLogDao())
    }

    @After
    fun tearDown() {
        database.close()
    }

    @Test
    fun insertAndRetrieveCallLog() = runTest {
        val callEvent = CallEvent(
            phoneNumber = "+**********",
            callState = CallState.FORWARDED,
            timestamp = Date(),
            callDirection = CallDirection.INCOMING,
            isForwarded = true,
            voipSessionId = "session123"
        )

        val id = repository.insertCallLog(callEvent)
        assertTrue(id > 0)

        val allLogs = repository.getAllCallLogs().first()
        assertEquals(1, allLogs.size)
        assertEquals(callEvent.phoneNumber, allLogs[0].phoneNumber)
        assertEquals(callEvent.callState, allLogs[0].callState)
        assertEquals(callEvent.isForwarded, allLogs[0].isForwarded)
    }

    @Test
    fun getForwardedCallsOnly() = runTest {
        // Insert forwarded call
        val forwardedCall = CallEvent(
            phoneNumber = "+1111111111",
            callState = CallState.FORWARDED,
            isForwarded = true
        )
        repository.insertCallLog(forwardedCall)

        // Insert non-forwarded call
        val normalCall = CallEvent(
            phoneNumber = "+2222222222",
            callState = CallState.ENDED,
            isForwarded = false
        )
        repository.insertCallLog(normalCall)

        val forwardedCalls = repository.getForwardedCalls().first()
        assertEquals(1, forwardedCalls.size)
        assertEquals("+1111111111", forwardedCalls[0].phoneNumber)
        assertTrue(forwardedCalls[0].isForwarded)
    }

    @Test
    fun getCallLogsByNumber() = runTest {
        val phoneNumber = "+**********"
        
        // Insert multiple calls for the same number
        repeat(3) { i ->
            val callEvent = CallEvent(
                phoneNumber = phoneNumber,
                callState = CallState.FORWARDED,
                timestamp = Date(System.currentTimeMillis() + i * 1000)
            )
            repository.insertCallLog(callEvent)
        }

        // Insert call for different number
        val otherCall = CallEvent(
            phoneNumber = "+9876543210",
            callState = CallState.ENDED
        )
        repository.insertCallLog(otherCall)

        val callsForNumber = repository.getCallLogsByNumber(phoneNumber).first()
        assertEquals(3, callsForNumber.size)
        assertTrue(callsForNumber.all { it.phoneNumber == phoneNumber })
    }

    @Test
    fun searchCallLogsByNumber() = runTest {
        // Insert calls with different numbers
        val numbers = listOf("+**********", "+1234555555", "+9876543210")
        numbers.forEach { number ->
            val callEvent = CallEvent(phoneNumber = number, callState = CallState.ENDED)
            repository.insertCallLog(callEvent)
        }

        // Search for numbers containing "1234"
        val searchResults = repository.searchCallLogsByNumber("1234").first()
        assertEquals(2, searchResults.size)
        assertTrue(searchResults.all { it.phoneNumber.contains("1234") })
    }

    @Test
    fun deleteAllCallLogs() = runTest {
        // Insert some call logs
        repeat(5) { i ->
            val callEvent = CallEvent(
                phoneNumber = "+123456789$i",
                callState = CallState.ENDED
            )
            repository.insertCallLog(callEvent)
        }

        // Verify logs exist
        val logsBeforeDelete = repository.getAllCallLogs().first()
        assertEquals(5, logsBeforeDelete.size)

        // Delete all logs
        repository.deleteAllCallLogs()

        // Verify logs are deleted
        val logsAfterDelete = repository.getAllCallLogs().first()
        assertEquals(0, logsAfterDelete.size)
    }
}
