<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">VoIP Call Forwarder</string>
    
    <!-- Main Activity -->
    <string name="title_call_forwarding">Call Forwarding</string>
    <string name="enable_forwarding">Enable Call Forwarding</string>
    <string name="disable_forwarding">Disable Call Forwarding</string>
    <string name="forwarding_status">Forwarding Status</string>
    <string name="status_active">Active</string>
    <string name="status_inactive">Inactive</string>
    <string name="status_connecting">Connecting...</string>
    <string name="status_error">Error</string>
    
    <!-- Configuration -->
    <string name="title_configuration">VoIP Configuration</string>
    <string name="sip_server_address">SIP Server Address</string>
    <string name="sip_server_port">SIP Server Port</string>
    <string name="sip_username">Username</string>
    <string name="sip_password">Password</string>
    <string name="sip_domain">Domain</string>
    <string name="forwarding_destination">Forwarding Destination</string>
    <string name="save_configuration">Save Configuration</string>
    <string name="test_connection">Test Connection</string>
    
    <!-- Call Logs -->
    <string name="title_call_logs">Call Logs</string>
    <string name="no_call_logs">No forwarded calls yet</string>
    <string name="call_forwarded">Forwarded</string>
    <string name="call_failed">Failed</string>
    <string name="call_duration">Duration: %s</string>
    <string name="clear_logs">Clear Logs</string>
    
    <!-- Settings -->
    <string name="title_settings">Settings</string>
    <string name="auto_start_on_boot">Auto-start on boot</string>
    <string name="show_call_notifications">Show call notifications</string>
    <string name="log_forwarded_calls">Log forwarded calls</string>
    
    <!-- Permissions -->
    <string name="permission_required">Permission Required</string>
    <string name="phone_permission_rationale">This app needs phone permissions to detect incoming calls and forward them via VoIP.</string>
    <string name="microphone_permission_rationale">This app needs microphone permission to handle audio during call forwarding.</string>
    <string name="grant_permission">Grant Permission</string>
    <string name="permission_denied">Permission denied. The app cannot function without required permissions.</string>
    
    <!-- Notifications -->
    <string name="notification_forwarding_active">Call forwarding is active</string>
    <string name="notification_incoming_call">Incoming call from %s</string>
    <string name="notification_call_forwarded">Call forwarded to %s</string>
    
    <!-- Errors -->
    <string name="error_invalid_configuration">Invalid VoIP configuration</string>
    <string name="error_connection_failed">Failed to connect to VoIP server</string>
    <string name="error_forwarding_failed">Call forwarding failed</string>
    <string name="error_no_forwarding_destination">No forwarding destination configured</string>
    
    <!-- Buttons -->
    <string name="ok">OK</string>
    <string name="cancel">Cancel</string>
    <string name="retry">Retry</string>
    <string name="settings">Settings</string>
    
    <!-- Hints -->
    <string name="hint_sip_server">e.g., sip.yourserver.com</string>
    <string name="hint_sip_port">e.g., 5060</string>
    <string name="hint_username">Your SIP username</string>
    <string name="hint_password">Your SIP password</string>
    <string name="hint_domain">e.g., yourserver.com</string>
    <string name="hint_destination">e.g., <EMAIL></string>
</resources>
