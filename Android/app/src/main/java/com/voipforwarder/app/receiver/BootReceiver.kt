package com.voipforwarder.app.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import com.voipforwarder.app.service.CallForwardingService
import com.voipforwarder.app.utils.Logger
import com.voipforwarder.app.utils.PreferenceManager

/**
 * BroadcastReceiver that starts the call forwarding service on device boot
 * if auto-start is enabled in preferences
 */
class BootReceiver : BroadcastReceiver() {

    companion object {
        private const val TAG = "BootReceiver"
    }

    override fun onReceive(context: Context, intent: Intent) {
        Logger.d(TAG, "Received broadcast: ${intent.action}")

        when (intent.action) {
            Intent.ACTION_BOOT_COMPLETED,
            Intent.ACTION_MY_PACKAGE_REPLACED,
            Intent.ACTION_PACKAGE_REPLACED -> {
                handleBootCompleted(context)
            }
        }
    }

    private fun handleBootCompleted(context: Context) {
        Logger.logServiceEvent("BootReceiver", "Boot completed")

        val preferenceManager = PreferenceManager(context)
        
        // Check if auto-start is enabled
        if (!preferenceManager.isAutoStartOnBootEnabled()) {
            Logger.d(TAG, "Auto-start on boot is disabled")
            return
        }

        // Check if forwarding was previously enabled
        if (!preferenceManager.isForwardingEnabled()) {
            Logger.d(TAG, "Call forwarding was not enabled before reboot")
            return
        }

        // Check if SIP configuration is available
        if (!preferenceManager.hasSipConfiguration()) {
            Logger.w(TAG, "SIP configuration not available, cannot start service")
            return
        }

        try {
            // Start the call forwarding service
            val serviceIntent = Intent(context, CallForwardingService::class.java).apply {
                action = CallForwardingService.ACTION_START_FORWARDING
            }

            context.startForegroundService(serviceIntent)
            Logger.logServiceEvent("BootReceiver", "Started CallForwardingService on boot")

        } catch (e: Exception) {
            Logger.e(TAG, "Failed to start CallForwardingService on boot", e)
        }
    }
}
