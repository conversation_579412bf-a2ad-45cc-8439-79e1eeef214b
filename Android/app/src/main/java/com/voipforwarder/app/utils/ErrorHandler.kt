package com.voipforwarder.app.utils

import android.content.Context
import com.voipforwarder.app.data.model.CallEvent
import com.voipforwarder.app.data.model.CallState
import com.voipforwarder.app.data.repository.CallLogRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.launch

/**
 * Centralized error handling for the VoIP Call Forwarder app
 * Provides error categorization, logging, and recovery strategies
 */
class ErrorHandler(
    private val context: Context,
    private val callLogRepository: CallLogRepository,
    private val coroutineScope: CoroutineScope
) {

    companion object {
        private const val TAG = "ErrorHandler"
    }

    /**
     * Handle VoIP connection errors
     */
    fun handleVoIPError(error: Exception, callEvent: CallEvent? = null): ErrorAction {
        Logger.e(TAG, "VoIP error occurred", error)
        
        val errorType = categorizeError(error)
        val errorAction = determineAction(errorType, error)
        
        // Log the error event if we have call context
        callEvent?.let { event ->
            coroutineScope.launch {
                val errorEvent = event.copy(
                    callState = CallState.FORWARD_FAILED,
                    errorMessage = "${errorType.name}: ${error.message}"
                )
                callLogRepository.insertCallLog(errorEvent)
            }
        }
        
        // Log error details
        Logger.logVoIPEvent("Error handled", "Type: ${errorType.name}, Action: ${errorAction.name}")
        
        return errorAction
    }

    /**
     * Handle call forwarding errors
     */
    fun handleCallForwardingError(error: Exception, phoneNumber: String): ErrorAction {
        Logger.e(TAG, "Call forwarding error for $phoneNumber", error)
        
        val errorType = categorizeError(error)
        val errorAction = determineAction(errorType, error)
        
        // Create error call event
        coroutineScope.launch {
            val errorEvent = CallEvent(
                phoneNumber = phoneNumber,
                callState = CallState.FORWARD_FAILED,
                errorMessage = "${errorType.name}: ${error.message}"
            )
            callLogRepository.insertCallLog(errorEvent)
        }
        
        Logger.logCallEvent("Call forwarding error handled", phoneNumber, "Type: ${errorType.name}")
        
        return errorAction
    }

    /**
     * Handle service errors
     */
    fun handleServiceError(serviceName: String, error: Exception): ErrorAction {
        Logger.e(TAG, "Service error in $serviceName", error)
        
        val errorType = categorizeError(error)
        val errorAction = determineAction(errorType, error)
        
        Logger.logServiceEvent(serviceName, "Error handled", "Type: ${errorType.name}, Action: ${errorAction.name}")
        
        return errorAction
    }

    /**
     * Categorize error types
     */
    private fun categorizeError(error: Exception): ErrorType {
        return when {
            // Network-related errors
            error is java.net.SocketException -> ErrorType.NETWORK_ERROR
            error is java.net.ConnectException -> ErrorType.CONNECTION_ERROR
            error is java.net.SocketTimeoutException -> ErrorType.TIMEOUT_ERROR
            error is java.io.IOException -> ErrorType.NETWORK_ERROR
            
            // VoIP-specific errors
            error.message?.contains("sip", ignoreCase = true) == true -> ErrorType.SIP_ERROR
            error.message?.contains("registration", ignoreCase = true) == true -> ErrorType.REGISTRATION_ERROR
            error.message?.contains("authentication", ignoreCase = true) == true -> ErrorType.AUTHENTICATION_ERROR
            error.message?.contains("unauthorized", ignoreCase = true) == true -> ErrorType.AUTHENTICATION_ERROR
            
            // Audio-related errors
            error.message?.contains("audio", ignoreCase = true) == true -> ErrorType.AUDIO_ERROR
            error.message?.contains("microphone", ignoreCase = true) == true -> ErrorType.AUDIO_ERROR
            
            // Permission errors
            error.message?.contains("permission", ignoreCase = true) == true -> ErrorType.PERMISSION_ERROR
            error is SecurityException -> ErrorType.PERMISSION_ERROR
            
            // Configuration errors
            error.message?.contains("configuration", ignoreCase = true) == true -> ErrorType.CONFIGURATION_ERROR
            error.message?.contains("invalid", ignoreCase = true) == true -> ErrorType.CONFIGURATION_ERROR
            
            // Service errors
            error.message?.contains("service", ignoreCase = true) == true -> ErrorType.SERVICE_ERROR
            
            // Default to unknown
            else -> ErrorType.UNKNOWN_ERROR
        }
    }

    /**
     * Determine appropriate action based on error type
     */
    private fun determineAction(errorType: ErrorType, error: Exception): ErrorAction {
        return when (errorType) {
            ErrorType.NETWORK_ERROR -> ErrorAction.RETRY_WITH_BACKOFF
            ErrorType.CONNECTION_ERROR -> ErrorAction.RETRY_WITH_BACKOFF
            ErrorType.TIMEOUT_ERROR -> ErrorAction.RETRY_IMMEDIATE
            ErrorType.SIP_ERROR -> ErrorAction.RETRY_WITH_BACKOFF
            ErrorType.REGISTRATION_ERROR -> ErrorAction.RETRY_WITH_BACKOFF
            ErrorType.AUTHENTICATION_ERROR -> ErrorAction.FAIL_PERMANENTLY
            ErrorType.AUDIO_ERROR -> ErrorAction.RETRY_IMMEDIATE
            ErrorType.PERMISSION_ERROR -> ErrorAction.FAIL_PERMANENTLY
            ErrorType.CONFIGURATION_ERROR -> ErrorAction.FAIL_PERMANENTLY
            ErrorType.SERVICE_ERROR -> ErrorAction.RESTART_SERVICE
            ErrorType.UNKNOWN_ERROR -> ErrorAction.RETRY_LIMITED
        }
    }

    /**
     * Get user-friendly error message
     */
    fun getUserFriendlyMessage(error: Exception): String {
        val errorType = categorizeError(error)
        
        return when (errorType) {
            ErrorType.NETWORK_ERROR -> "Network connection error. Please check your internet connection."
            ErrorType.CONNECTION_ERROR -> "Unable to connect to VoIP server. Please check server settings."
            ErrorType.TIMEOUT_ERROR -> "Connection timeout. The server may be busy."
            ErrorType.SIP_ERROR -> "VoIP service error. Please try again."
            ErrorType.REGISTRATION_ERROR -> "Failed to register with VoIP server. Check your credentials."
            ErrorType.AUTHENTICATION_ERROR -> "Authentication failed. Please check your username and password."
            ErrorType.AUDIO_ERROR -> "Audio system error. Please check microphone permissions."
            ErrorType.PERMISSION_ERROR -> "Missing required permissions. Please grant all permissions."
            ErrorType.CONFIGURATION_ERROR -> "Invalid configuration. Please check your VoIP settings."
            ErrorType.SERVICE_ERROR -> "Service error. Restarting service..."
            ErrorType.UNKNOWN_ERROR -> "An unexpected error occurred. Please try again."
        }
    }

    /**
     * Check if error is recoverable
     */
    fun isRecoverable(error: Exception): Boolean {
        val errorType = categorizeError(error)
        val action = determineAction(errorType, error)
        
        return when (action) {
            ErrorAction.RETRY_IMMEDIATE,
            ErrorAction.RETRY_WITH_BACKOFF,
            ErrorAction.RETRY_LIMITED,
            ErrorAction.RESTART_SERVICE -> true
            ErrorAction.FAIL_PERMANENTLY -> false
        }
    }

    /**
     * Get retry configuration for error
     */
    fun getRetryConfig(error: Exception): RetryConfig {
        val errorType = categorizeError(error)
        
        return when (errorType) {
            ErrorType.NETWORK_ERROR -> RetryConfigs.networkOperation
            ErrorType.CONNECTION_ERROR -> RetryConfigs.voipConnection
            ErrorType.TIMEOUT_ERROR -> RetryConfigs.quickRetry
            ErrorType.SIP_ERROR -> RetryConfigs.voipConnection
            ErrorType.REGISTRATION_ERROR -> RetryConfigs.voipConnection
            ErrorType.AUDIO_ERROR -> RetryConfigs.quickRetry
            ErrorType.SERVICE_ERROR -> RetryConfigs.quickRetry
            else -> RetryConfigs.networkOperation
        }
    }
}

/**
 * Types of errors that can occur in the app
 */
enum class ErrorType {
    NETWORK_ERROR,
    CONNECTION_ERROR,
    TIMEOUT_ERROR,
    SIP_ERROR,
    REGISTRATION_ERROR,
    AUTHENTICATION_ERROR,
    AUDIO_ERROR,
    PERMISSION_ERROR,
    CONFIGURATION_ERROR,
    SERVICE_ERROR,
    UNKNOWN_ERROR
}

/**
 * Actions to take when an error occurs
 */
enum class ErrorAction {
    RETRY_IMMEDIATE,        // Retry immediately
    RETRY_WITH_BACKOFF,     // Retry with exponential backoff
    RETRY_LIMITED,          // Retry with limited attempts
    RESTART_SERVICE,        // Restart the service
    FAIL_PERMANENTLY        // Don't retry, permanent failure
}

/**
 * Error recovery strategies
 */
object ErrorRecovery {
    
    /**
     * Attempt to recover from VoIP connection error
     */
    suspend fun recoverVoIPConnection(
        retryManager: RetryManager,
        voipInitializer: suspend () -> Boolean
    ): Boolean {
        Logger.d("ErrorRecovery", "Attempting VoIP connection recovery")
        
        val result = retryManager.executeWithRetryIf(
            operation = voipInitializer,
            shouldRetry = RetryConditions.voipErrors,
            config = RetryConfigs.voipConnection
        )
        
        return when (result) {
            is RetryResult.Success -> {
                Logger.d("ErrorRecovery", "VoIP connection recovered after ${result.attemptCount} attempts")
                true
            }
            is RetryResult.Failure -> {
                Logger.e("ErrorRecovery", "Failed to recover VoIP connection after ${result.attemptCount} attempts", result.exception)
                false
            }
        }
    }
    
    /**
     * Attempt to recover from call forwarding error
     */
    suspend fun recoverCallForwarding(
        retryManager: RetryManager,
        callForwarder: suspend () -> Boolean
    ): Boolean {
        Logger.d("ErrorRecovery", "Attempting call forwarding recovery")
        
        val result = retryManager.executeWithRetryIf(
            operation = callForwarder,
            shouldRetry = RetryConditions.temporaryErrors,
            config = RetryConfigs.callForwarding
        )
        
        return when (result) {
            is RetryResult.Success -> {
                Logger.d("ErrorRecovery", "Call forwarding recovered after ${result.attemptCount} attempts")
                true
            }
            is RetryResult.Failure -> {
                Logger.e("ErrorRecovery", "Failed to recover call forwarding after ${result.attemptCount} attempts", result.exception)
                false
            }
        }
    }
}
