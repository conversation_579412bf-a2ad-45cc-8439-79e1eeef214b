package com.voipforwarder.app.utils

import android.content.Context
import android.content.SharedPreferences
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey

/**
 * Secure preference manager for storing VoIP configuration and app settings
 */
class PreferenceManager(private val context: Context) {

    companion object {
        private const val PREFS_NAME = "voip_forwarder_prefs"
        private const val SECURE_PREFS_NAME = "voip_credentials"
        
        // Preference keys
        private const val KEY_FORWARDING_ENABLED = "forwarding_enabled"
        private const val KEY_FORWARDING_DESTINATION = "forwarding_destination"
        private const val KEY_SIP_SERVER_ADDRESS = "sip_server_address"
        private const val KEY_SIP_SERVER_PORT = "sip_server_port"
        private const val KEY_SIP_USERNAME = "sip_username"
        private const val KEY_SIP_PASSWORD = "sip_password"
        private const val KEY_SIP_DOMAIN = "sip_domain"
        private const val KEY_AUTO_START_ON_BOOT = "auto_start_on_boot"
        private const val KEY_SHOW_CALL_NOTIFICATIONS = "show_call_notifications"
        private const val KEY_LOG_FORWARDED_CALLS = "log_forwarded_calls"
    }

    // Regular preferences for non-sensitive data
    private val preferences: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    // Encrypted preferences for sensitive data (SIP credentials)
    private val securePreferences: SharedPreferences by lazy {
        val masterKey = MasterKey.Builder(context)
            .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
            .build()
            
        EncryptedSharedPreferences.create(
            context,
            SECURE_PREFS_NAME,
            masterKey,
            EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
            EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
        )
    }

    // Forwarding settings
    fun setForwardingEnabled(enabled: Boolean) {
        preferences.edit().putBoolean(KEY_FORWARDING_ENABLED, enabled).apply()
    }

    fun isForwardingEnabled(): Boolean {
        return preferences.getBoolean(KEY_FORWARDING_ENABLED, false)
    }

    fun setForwardingDestination(destination: String) {
        preferences.edit().putString(KEY_FORWARDING_DESTINATION, destination).apply()
    }

    fun getForwardingDestination(): String? {
        return preferences.getString(KEY_FORWARDING_DESTINATION, null)
    }

    // SIP server configuration (stored securely)
    fun setSipServerAddress(address: String) {
        securePreferences.edit().putString(KEY_SIP_SERVER_ADDRESS, address).apply()
    }

    fun getSipServerAddress(): String? {
        return securePreferences.getString(KEY_SIP_SERVER_ADDRESS, null)
    }

    fun setSipServerPort(port: Int) {
        securePreferences.edit().putInt(KEY_SIP_SERVER_PORT, port).apply()
    }

    fun getSipServerPort(): Int? {
        val port = securePreferences.getInt(KEY_SIP_SERVER_PORT, -1)
        return if (port == -1) null else port
    }

    fun setSipUsername(username: String) {
        securePreferences.edit().putString(KEY_SIP_USERNAME, username).apply()
    }

    fun getSipUsername(): String? {
        return securePreferences.getString(KEY_SIP_USERNAME, null)
    }

    fun setSipPassword(password: String) {
        securePreferences.edit().putString(KEY_SIP_PASSWORD, password).apply()
    }

    fun getSipPassword(): String? {
        return securePreferences.getString(KEY_SIP_PASSWORD, null)
    }

    fun setSipDomain(domain: String) {
        securePreferences.edit().putString(KEY_SIP_DOMAIN, domain).apply()
    }

    fun getSipDomain(): String? {
        return securePreferences.getString(KEY_SIP_DOMAIN, null)
    }

    // App settings
    fun setAutoStartOnBoot(enabled: Boolean) {
        preferences.edit().putBoolean(KEY_AUTO_START_ON_BOOT, enabled).apply()
    }

    fun isAutoStartOnBootEnabled(): Boolean {
        return preferences.getBoolean(KEY_AUTO_START_ON_BOOT, true)
    }

    fun setShowCallNotifications(enabled: Boolean) {
        preferences.edit().putBoolean(KEY_SHOW_CALL_NOTIFICATIONS, enabled).apply()
    }

    fun shouldShowCallNotifications(): Boolean {
        return preferences.getBoolean(KEY_SHOW_CALL_NOTIFICATIONS, true)
    }

    fun setLogForwardedCalls(enabled: Boolean) {
        preferences.edit().putBoolean(KEY_LOG_FORWARDED_CALLS, enabled).apply()
    }

    fun shouldLogForwardedCalls(): Boolean {
        return preferences.getBoolean(KEY_LOG_FORWARDED_CALLS, true)
    }

    // Utility methods
    fun clearAllPreferences() {
        preferences.edit().clear().apply()
        securePreferences.edit().clear().apply()
    }

    fun clearSipCredentials() {
        securePreferences.edit()
            .remove(KEY_SIP_USERNAME)
            .remove(KEY_SIP_PASSWORD)
            .remove(KEY_SIP_SERVER_ADDRESS)
            .remove(KEY_SIP_SERVER_PORT)
            .remove(KEY_SIP_DOMAIN)
            .apply()
    }

    fun hasSipConfiguration(): Boolean {
        return !getSipServerAddress().isNullOrEmpty() &&
               !getSipUsername().isNullOrEmpty() &&
               !getSipPassword().isNullOrEmpty() &&
               !getSipDomain().isNullOrEmpty()
    }
}
