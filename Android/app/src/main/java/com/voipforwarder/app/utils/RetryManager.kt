package com.voipforwarder.app.utils

import kotlinx.coroutines.delay
import kotlin.math.min
import kotlin.math.pow

/**
 * Retry manager for handling failed operations with exponential backoff
 * Provides configurable retry strategies for VoIP connections and other operations
 */
class RetryManager {

    companion object {
        private const val TAG = "RetryManager"
        
        // Default retry configurations
        const val DEFAULT_MAX_RETRIES = 3
        const val DEFAULT_INITIAL_DELAY_MS = 1000L
        const val DEFAULT_MAX_DELAY_MS = 30000L
        const val DEFAULT_BACKOFF_MULTIPLIER = 2.0
    }

    /**
     * Execute an operation with retry logic
     */
    suspend fun <T> executeWithRetry(
        operation: suspend () -> T,
        config: RetryConfig = RetryConfig()
    ): RetryResult<T> {
        var lastException: Exception? = null
        var attempt = 0
        
        while (attempt <= config.maxRetries) {
            try {
                Logger.d(TAG, "Executing operation, attempt ${attempt + 1}/${config.maxRetries + 1}")
                val result = operation()
                
                if (attempt > 0) {
                    Logger.d(TAG, "Operation succeeded after $attempt retries")
                }
                
                return RetryResult.Success(result, attempt)
                
            } catch (e: Exception) {
                lastException = e
                attempt++
                
                Logger.w(TAG, "Operation failed on attempt $attempt: ${e.message}")
                
                if (attempt <= config.maxRetries) {
                    val delay = calculateDelay(attempt - 1, config)
                    Logger.d(TAG, "Retrying in ${delay}ms...")
                    delay(delay)
                } else {
                    Logger.e(TAG, "Operation failed after ${config.maxRetries + 1} attempts", e)
                }
            }
        }
        
        return RetryResult.Failure(lastException ?: Exception("Unknown error"), attempt - 1)
    }

    /**
     * Execute an operation with retry logic and custom retry condition
     */
    suspend fun <T> executeWithRetryIf(
        operation: suspend () -> T,
        shouldRetry: (Exception) -> Boolean,
        config: RetryConfig = RetryConfig()
    ): RetryResult<T> {
        var lastException: Exception? = null
        var attempt = 0
        
        while (attempt <= config.maxRetries) {
            try {
                Logger.d(TAG, "Executing operation with condition, attempt ${attempt + 1}/${config.maxRetries + 1}")
                val result = operation()
                
                if (attempt > 0) {
                    Logger.d(TAG, "Operation succeeded after $attempt retries")
                }
                
                return RetryResult.Success(result, attempt)
                
            } catch (e: Exception) {
                lastException = e
                attempt++
                
                Logger.w(TAG, "Operation failed on attempt $attempt: ${e.message}")
                
                if (attempt <= config.maxRetries && shouldRetry(e)) {
                    val delay = calculateDelay(attempt - 1, config)
                    Logger.d(TAG, "Retrying in ${delay}ms...")
                    delay(delay)
                } else {
                    Logger.e(TAG, "Operation failed or retry condition not met after $attempt attempts", e)
                    break
                }
            }
        }
        
        return RetryResult.Failure(lastException ?: Exception("Unknown error"), attempt - 1)
    }

    /**
     * Calculate delay for exponential backoff
     */
    private fun calculateDelay(attemptNumber: Int, config: RetryConfig): Long {
        val exponentialDelay = config.initialDelayMs * config.backoffMultiplier.pow(attemptNumber).toLong()
        return min(exponentialDelay, config.maxDelayMs)
    }
}

/**
 * Configuration for retry behavior
 */
data class RetryConfig(
    val maxRetries: Int = RetryManager.DEFAULT_MAX_RETRIES,
    val initialDelayMs: Long = RetryManager.DEFAULT_INITIAL_DELAY_MS,
    val maxDelayMs: Long = RetryManager.DEFAULT_MAX_DELAY_MS,
    val backoffMultiplier: Double = RetryManager.DEFAULT_BACKOFF_MULTIPLIER
)

/**
 * Result of a retry operation
 */
sealed class RetryResult<T> {
    data class Success<T>(val value: T, val attemptCount: Int) : RetryResult<T>()
    data class Failure<T>(val exception: Exception, val attemptCount: Int) : RetryResult<T>()
    
    fun isSuccess(): Boolean = this is Success
    fun isFailure(): Boolean = this is Failure
    
    fun getOrNull(): T? = when (this) {
        is Success -> value
        is Failure -> null
    }
    
    fun getOrThrow(): T = when (this) {
        is Success -> value
        is Failure -> throw exception
    }
}

/**
 * Predefined retry conditions for common scenarios
 */
object RetryConditions {
    
    /**
     * Retry on network-related exceptions
     */
    val networkErrors: (Exception) -> Boolean = { exception ->
        when {
            exception.message?.contains("network", ignoreCase = true) == true -> true
            exception.message?.contains("connection", ignoreCase = true) == true -> true
            exception.message?.contains("timeout", ignoreCase = true) == true -> true
            exception.message?.contains("unreachable", ignoreCase = true) == true -> true
            exception is java.net.SocketException -> true
            exception is java.net.ConnectException -> true
            exception is java.net.SocketTimeoutException -> true
            exception is java.io.IOException -> true
            else -> false
        }
    }
    
    /**
     * Retry on VoIP-specific errors
     */
    val voipErrors: (Exception) -> Boolean = { exception ->
        when {
            exception.message?.contains("sip", ignoreCase = true) == true -> true
            exception.message?.contains("registration", ignoreCase = true) == true -> true
            exception.message?.contains("authentication", ignoreCase = true) == true -> false // Don't retry auth errors
            exception.message?.contains("unauthorized", ignoreCase = true) == true -> false
            networkErrors(exception) -> true
            else -> false
        }
    }
    
    /**
     * Retry on temporary errors only
     */
    val temporaryErrors: (Exception) -> Boolean = { exception ->
        when {
            exception.message?.contains("temporary", ignoreCase = true) == true -> true
            exception.message?.contains("busy", ignoreCase = true) == true -> true
            exception.message?.contains("unavailable", ignoreCase = true) == true -> true
            exception.message?.contains("503", ignoreCase = true) == true -> true
            exception.message?.contains("502", ignoreCase = true) == true -> true
            networkErrors(exception) -> true
            else -> false
        }
    }
}

/**
 * Retry configurations for different scenarios
 */
object RetryConfigs {
    
    /**
     * Configuration for VoIP connection attempts
     */
    val voipConnection = RetryConfig(
        maxRetries = 5,
        initialDelayMs = 2000L,
        maxDelayMs = 30000L,
        backoffMultiplier = 2.0
    )
    
    /**
     * Configuration for call forwarding attempts
     */
    val callForwarding = RetryConfig(
        maxRetries = 3,
        initialDelayMs = 1000L,
        maxDelayMs = 15000L,
        backoffMultiplier = 2.0
    )
    
    /**
     * Configuration for network operations
     */
    val networkOperation = RetryConfig(
        maxRetries = 3,
        initialDelayMs = 500L,
        maxDelayMs = 10000L,
        backoffMultiplier = 2.0
    )
    
    /**
     * Configuration for quick retries
     */
    val quickRetry = RetryConfig(
        maxRetries = 2,
        initialDelayMs = 500L,
        maxDelayMs = 2000L,
        backoffMultiplier = 1.5
    )
}
