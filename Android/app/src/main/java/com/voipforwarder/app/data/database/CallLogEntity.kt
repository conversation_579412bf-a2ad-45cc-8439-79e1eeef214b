package com.voipforwarder.app.data.database

import androidx.room.Entity
import androidx.room.PrimaryKey
import androidx.room.TypeConverter
import androidx.room.TypeConverters
import com.voipforwarder.app.data.model.CallDirection
import com.voipforwarder.app.data.model.CallState
import java.util.Date

/**
 * Room entity for storing call log entries
 */
@Entity(tableName = "call_logs")
@TypeConverters(CallLogConverters::class)
data class CallLogEntity(
    @PrimaryKey(autoGenerate = true)
    val id: Long = 0,
    val phoneNumber: String,
    val callState: CallState,
    val timestamp: Date,
    val callDirection: CallDirection,
    val duration: Long = 0L, // Duration in milliseconds
    val isForwarded: Boolean = false,
    val voipSessionId: String? = null,
    val errorMessage: String? = null,
    val forwardingDestination: String? = null,
    val sipServerAddress: String? = null
)

/**
 * Type converters for Room database
 */
class CallLogConverters {
    
    @TypeConverter
    fun fromTimestamp(value: Long?): Date? {
        return value?.let { Date(it) }
    }

    @TypeConverter
    fun dateToTimestamp(date: Date?): Long? {
        return date?.time
    }

    @TypeConverter
    fun fromCallState(callState: CallState): String {
        return callState.name
    }

    @TypeConverter
    fun toCallState(callState: String): CallState {
        return CallState.valueOf(callState)
    }

    @TypeConverter
    fun fromCallDirection(callDirection: CallDirection): String {
        return callDirection.name
    }

    @TypeConverter
    fun toCallDirection(callDirection: String): CallDirection {
        return CallDirection.valueOf(callDirection)
    }
}
