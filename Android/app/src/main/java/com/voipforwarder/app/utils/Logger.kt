package com.voipforwarder.app.utils

import android.util.Log
import timber.log.Timber

/**
 * Centralized logging utility for the VoIP Call Forwarder app
 * Provides structured logging with different levels and automatic tagging
 */
object Logger {
    
    private const val DEFAULT_TAG = "VoIPForwarder"
    
    fun d(tag: String = DEFAULT_TAG, message: String) {
        Timber.tag(tag).d(message)
    }
    
    fun i(tag: String = DEFAULT_TAG, message: String) {
        Timber.tag(tag).i(message)
    }
    
    fun w(tag: String = DEFAULT_TAG, message: String, throwable: Throwable? = null) {
        if (throwable != null) {
            Timber.tag(tag).w(throwable, message)
        } else {
            Timber.tag(tag).w(message)
        }
    }
    
    fun e(tag: String = DEFAULT_TAG, message: String, throwable: Throwable? = null) {
        if (throwable != null) {
            Timber.tag(tag).e(throwable, message)
        } else {
            Timber.tag(tag).e(message)
        }
    }
    
    // Specific logging methods for different components
    fun logCallEvent(event: String, phoneNumber: String? = null, details: String? = null) {
        val message = buildString {
            append("Call Event: $event")
            phoneNumber?.let { append(" | Number: $it") }
            details?.let { append(" | Details: $it") }
        }
        i("CallEvents", message)
    }
    
    fun logVoIPEvent(event: String, details: String? = null) {
        val message = buildString {
            append("VoIP Event: $event")
            details?.let { append(" | Details: $it") }
        }
        i("VoIPEvents", message)
    }
    
    fun logServiceEvent(service: String, event: String, details: String? = null) {
        val message = buildString {
            append("Service: $service | Event: $event")
            details?.let { append(" | Details: $it") }
        }
        i("ServiceEvents", message)
    }
}
