package com.voipforwarder.app.data.database

import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.TypeConverters
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import android.content.Context

/**
 * Room database for storing call logs and related data
 */
@Database(
    entities = [CallLogEntity::class],
    version = 1,
    exportSchema = false
)
@TypeConverters(CallLogConverters::class)
abstract class CallLogDatabase : RoomDatabase() {

    abstract fun callLogDao(): CallLogDao

    companion object {
        private const val DATABASE_NAME = "call_log_database"

        @Volatile
        private var INSTANCE: CallLogDatabase? = null

        fun getDatabase(context: Context): CallLogDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    CallLogDatabase::class.java,
                    DATABASE_NAME
                )
                    .addMigrations(MIGRATION_1_2) // Add migrations as needed
                    .build()
                INSTANCE = instance
                instance
            }
        }

        /**
         * Example migration for future database schema changes
         */
        private val MIGRATION_1_2 = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
                // Example: Add a new column
                // database.execSQL("ALTER TABLE call_logs ADD COLUMN newColumn TEXT")
            }
        }
    }
}
