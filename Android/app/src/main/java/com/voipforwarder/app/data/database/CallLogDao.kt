package com.voipforwarder.app.data.database

import androidx.room.*
import com.voipforwarder.app.data.model.CallState
import kotlinx.coroutines.flow.Flow
import java.util.Date

/**
 * Data Access Object for call log operations
 */
@Dao
interface CallLogDao {

    /**
     * Insert a new call log entry
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCallLog(callLog: CallLogEntity): Long

    /**
     * Insert multiple call log entries
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertCallLogs(callLogs: List<CallLogEntity>): List<Long>

    /**
     * Update an existing call log entry
     */
    @Update
    suspend fun updateCallLog(callLog: CallLogEntity)

    /**
     * Delete a call log entry
     */
    @Delete
    suspend fun deleteCallLog(callLog: CallLogEntity)

    /**
     * Delete all call logs
     */
    @Query("DELETE FROM call_logs")
    suspend fun deleteAllCallLogs()

    /**
     * Get all call logs ordered by timestamp (newest first)
     */
    @Query("SELECT * FROM call_logs ORDER BY timestamp DESC")
    fun getAllCallLogs(): Flow<List<CallLogEntity>>

    /**
     * Get call logs with pagination
     */
    @Query("SELECT * FROM call_logs ORDER BY timestamp DESC LIMIT :limit OFFSET :offset")
    suspend fun getCallLogsPaged(limit: Int, offset: Int): List<CallLogEntity>

    /**
     * Get call logs by phone number
     */
    @Query("SELECT * FROM call_logs WHERE phoneNumber = :phoneNumber ORDER BY timestamp DESC")
    fun getCallLogsByNumber(phoneNumber: String): Flow<List<CallLogEntity>>

    /**
     * Get call logs by call state
     */
    @Query("SELECT * FROM call_logs WHERE callState = :callState ORDER BY timestamp DESC")
    fun getCallLogsByState(callState: CallState): Flow<List<CallLogEntity>>

    /**
     * Get only forwarded calls
     */
    @Query("SELECT * FROM call_logs WHERE isForwarded = 1 ORDER BY timestamp DESC")
    fun getForwardedCalls(): Flow<List<CallLogEntity>>

    /**
     * Get failed forwarding attempts
     */
    @Query("SELECT * FROM call_logs WHERE callState = 'FORWARD_FAILED' ORDER BY timestamp DESC")
    fun getFailedForwardingAttempts(): Flow<List<CallLogEntity>>

    /**
     * Get call logs within a date range
     */
    @Query("SELECT * FROM call_logs WHERE timestamp BETWEEN :startDate AND :endDate ORDER BY timestamp DESC")
    fun getCallLogsInDateRange(startDate: Date, endDate: Date): Flow<List<CallLogEntity>>

    /**
     * Get call log by VoIP session ID
     */
    @Query("SELECT * FROM call_logs WHERE voipSessionId = :sessionId LIMIT 1")
    suspend fun getCallLogBySessionId(sessionId: String): CallLogEntity?

    /**
     * Get statistics - total forwarded calls
     */
    @Query("SELECT COUNT(*) FROM call_logs WHERE isForwarded = 1")
    suspend fun getTotalForwardedCalls(): Int

    /**
     * Get statistics - total failed calls
     */
    @Query("SELECT COUNT(*) FROM call_logs WHERE callState = 'FORWARD_FAILED'")
    suspend fun getTotalFailedCalls(): Int

    /**
     * Get statistics - calls in the last 24 hours
     */
    @Query("SELECT COUNT(*) FROM call_logs WHERE timestamp > :since")
    suspend fun getCallsCountSince(since: Date): Int

    /**
     * Get average call duration for forwarded calls
     */
    @Query("SELECT AVG(duration) FROM call_logs WHERE isForwarded = 1 AND duration > 0")
    suspend fun getAverageCallDuration(): Double?

    /**
     * Search call logs by phone number pattern
     */
    @Query("SELECT * FROM call_logs WHERE phoneNumber LIKE :pattern ORDER BY timestamp DESC")
    fun searchCallLogsByNumber(pattern: String): Flow<List<CallLogEntity>>

    /**
     * Get recent call logs (last 50)
     */
    @Query("SELECT * FROM call_logs ORDER BY timestamp DESC LIMIT 50")
    fun getRecentCallLogs(): Flow<List<CallLogEntity>>

    /**
     * Delete old call logs (older than specified date)
     */
    @Query("DELETE FROM call_logs WHERE timestamp < :cutoffDate")
    suspend fun deleteOldCallLogs(cutoffDate: Date): Int

    /**
     * Get call logs grouped by date
     */
    @Query("""
        SELECT DATE(timestamp/1000, 'unixepoch') as date, COUNT(*) as count 
        FROM call_logs 
        GROUP BY DATE(timestamp/1000, 'unixepoch') 
        ORDER BY date DESC
    """)
    suspend fun getCallLogsByDate(): List<CallLogDateCount>
}

/**
 * Data class for call logs grouped by date
 */
data class CallLogDateCount(
    val date: String,
    val count: Int
)
