package com.voipforwarder.app

import android.app.Application
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context
import android.os.Build
import androidx.room.Room
import com.voipforwarder.app.data.database.CallLogDatabase
import com.voipforwarder.app.utils.Logger
import timber.log.Timber

/**
 * Application class for VoIP Call Forwarder
 * Initializes logging, database, and notification channels
 */
class VoIPForwarderApplication : Application() {

    companion object {
        const val CALL_FORWARDING_CHANNEL_ID = "call_forwarding_channel"
        const val VOIP_SERVICE_CHANNEL_ID = "voip_service_channel"
        const val INCOMING_CALL_CHANNEL_ID = "incoming_call_channel"
        
        lateinit var instance: VoIPForwarderApplication
            private set
    }

    // Database instance
    val database by lazy {
        Room.databaseBuilder(
            applicationContext,
            CallLogDatabase::class.java,
            "call_log_database"
        ).build()
    }

    override fun onCreate() {
        super.onCreate()
        instance = this
        
        // Initialize logging
        initializeLogging()
        
        // Create notification channels
        createNotificationChannels()
        
        Logger.d("VoIPForwarderApplication", "Application initialized")
    }

    private fun initializeLogging() {
        if (BuildConfig.DEBUG) {
            Timber.plant(Timber.DebugTree())
        }
        // In production, you might want to plant a custom tree for crash reporting
    }

    private fun createNotificationChannels() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager

            // Call Forwarding Service Channel
            val callForwardingChannel = NotificationChannel(
                CALL_FORWARDING_CHANNEL_ID,
                "Call Forwarding Service",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "Persistent notification for call forwarding service"
                setShowBadge(false)
            }

            // VoIP Service Channel
            val voipServiceChannel = NotificationChannel(
                VOIP_SERVICE_CHANNEL_ID,
                "VoIP Service",
                NotificationManager.IMPORTANCE_LOW
            ).apply {
                description = "VoIP connection status notifications"
                setShowBadge(false)
            }

            // Incoming Call Channel
            val incomingCallChannel = NotificationChannel(
                INCOMING_CALL_CHANNEL_ID,
                "Incoming Calls",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "Notifications for incoming calls being forwarded"
                setShowBadge(true)
            }

            notificationManager.createNotificationChannels(
                listOf(callForwardingChannel, voipServiceChannel, incomingCallChannel)
            )
        }
    }
}
