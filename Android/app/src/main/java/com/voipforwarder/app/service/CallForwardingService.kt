package com.voipforwarder.app.service

import android.app.Notification
import android.app.NotificationManager
import android.app.PendingIntent
import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Binder
import android.os.IBinder
import androidx.core.app.NotificationCompat
import androidx.lifecycle.lifecycleScope
import com.voipforwarder.app.R
import com.voipforwarder.app.VoIPForwarderApplication
import com.voipforwarder.app.data.model.CallEvent
import com.voipforwarder.app.data.model.CallState
import com.voipforwarder.app.data.repository.CallLogRepository
import com.voipforwarder.app.ui.MainActivity
import com.voipforwarder.app.utils.Logger
import com.voipforwarder.app.utils.PreferenceManager
import com.voipforwarder.app.voip.LinphoneVoIPManager
import com.voipforwarder.app.voip.VoIPConfig
import com.voipforwarder.app.voip.VoIPManager
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.launch
import java.util.concurrent.ConcurrentHashMap

/**
 * Foreground service that handles call forwarding operations
 * Runs continuously to monitor calls and manage VoIP sessions
 */
class CallForwardingService : Service() {

    companion object {
        private const val TAG = "CallForwardingService"
        private const val NOTIFICATION_ID = 1001
        
        // Service actions
        const val ACTION_START_FORWARDING = "com.voipforwarder.app.START_FORWARDING"
        const val ACTION_STOP_FORWARDING = "com.voipforwarder.app.STOP_FORWARDING"
        const val ACTION_INCOMING_CALL = "com.voipforwarder.app.INCOMING_CALL"
        const val ACTION_CALL_ANSWERED = "com.voipforwarder.app.CALL_ANSWERED"
        const val ACTION_CALL_ENDED = "com.voipforwarder.app.CALL_ENDED"
        
        // Intent extras
        const val EXTRA_CALL_EVENT = "call_event"
        const val EXTRA_PHONE_NUMBER = "phone_number"
    }

    // Service binder for UI communication
    inner class CallForwardingBinder : Binder() {
        fun getService(): CallForwardingService = this@CallForwardingService
    }

    private val binder = CallForwardingBinder()
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Main)
    
    // Dependencies
    private lateinit var voipManager: VoIPManager
    private lateinit var callLogRepository: CallLogRepository
    private lateinit var preferenceManager: PreferenceManager
    
    // State management
    private var isForwardingEnabled = false
    private val activeCallSessions = ConcurrentHashMap<String, CallEvent>()
    private var currentForwardingJob: Job? = null

    override fun onCreate() {
        super.onCreate()
        Logger.logServiceEvent("CallForwardingService", "onCreate")
        
        // Initialize dependencies
        voipManager = LinphoneVoIPManager()
        callLogRepository = CallLogRepository(VoIPForwarderApplication.instance.database.callLogDao())
        preferenceManager = PreferenceManager(this)
        
        // Start as foreground service
        startForeground(NOTIFICATION_ID, createNotification("Call forwarding service started"))
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        Logger.logServiceEvent("CallForwardingService", "onStartCommand", "Action: ${intent?.action}")
        
        when (intent?.action) {
            ACTION_START_FORWARDING -> startCallForwarding()
            ACTION_STOP_FORWARDING -> stopCallForwarding()
            ACTION_INCOMING_CALL -> handleIncomingCall(intent)
            ACTION_CALL_ANSWERED -> handleCallAnswered(intent)
            ACTION_CALL_ENDED -> handleCallEnded(intent)
        }
        
        return START_STICKY // Restart service if killed
    }

    override fun onBind(intent: Intent?): IBinder = binder

    override fun onDestroy() {
        Logger.logServiceEvent("CallForwardingService", "onDestroy")
        
        // Cleanup resources
        currentForwardingJob?.cancel()
        serviceScope.launch {
            voipManager.cleanup()
        }
        
        super.onDestroy()
    }

    private fun startCallForwarding() {
        Logger.logServiceEvent("CallForwardingService", "startCallForwarding")
        
        if (isForwardingEnabled) {
            Logger.d(TAG, "Call forwarding already enabled")
            return
        }
        
        serviceScope.launch {
            try {
                // Initialize VoIP manager with configuration
                val config = getVoIPConfiguration()
                val initialized = voipManager.initialize(this@CallForwardingService, config)
                
                if (initialized) {
                    isForwardingEnabled = true
                    updateNotification("Call forwarding active - Ready to forward calls")
                    Logger.logServiceEvent("CallForwardingService", "Call forwarding enabled")
                } else {
                    Logger.e(TAG, "Failed to initialize VoIP manager")
                    updateNotification("Call forwarding failed - Check configuration")
                }
                
            } catch (e: Exception) {
                Logger.e(TAG, "Error starting call forwarding", e)
                updateNotification("Call forwarding error - ${e.message}")
            }
        }
    }

    private fun stopCallForwarding() {
        Logger.logServiceEvent("CallForwardingService", "stopCallForwarding")
        
        if (!isForwardingEnabled) {
            Logger.d(TAG, "Call forwarding already disabled")
            return
        }
        
        serviceScope.launch {
            try {
                // End any active VoIP sessions
                activeCallSessions.keys.forEach { sessionId ->
                    voipManager.endCall(sessionId)
                }
                activeCallSessions.clear()
                
                // Cleanup VoIP manager
                voipManager.cleanup()
                
                isForwardingEnabled = false
                updateNotification("Call forwarding stopped")
                Logger.logServiceEvent("CallForwardingService", "Call forwarding disabled")
                
            } catch (e: Exception) {
                Logger.e(TAG, "Error stopping call forwarding", e)
            }
        }
    }

    private fun handleIncomingCall(intent: Intent) {
        val callEvent = intent.getParcelableExtra<CallEvent>(EXTRA_CALL_EVENT)
        if (callEvent == null) {
            Logger.w(TAG, "Received incoming call intent without call event")
            return
        }
        
        Logger.logCallEvent("Handling incoming call", callEvent.phoneNumber)
        
        if (!isForwardingEnabled) {
            Logger.d(TAG, "Call forwarding disabled, ignoring incoming call")
            return
        }
        
        // Start call forwarding in background
        currentForwardingJob = serviceScope.launch {
            forwardCall(callEvent)
        }
    }

    private fun handleCallAnswered(intent: Intent) {
        val phoneNumber = intent.getStringExtra(EXTRA_PHONE_NUMBER)
        Logger.logCallEvent("Call answered", phoneNumber)
        
        // Update active sessions
        activeCallSessions.values.forEach { callEvent ->
            if (callEvent.phoneNumber == phoneNumber) {
                val updatedEvent = callEvent.copy(callState = CallState.OFFHOOK)
                activeCallSessions[callEvent.voipSessionId ?: ""] = updatedEvent
            }
        }
    }

    private fun handleCallEnded(intent: Intent) {
        val phoneNumber = intent.getStringExtra(EXTRA_PHONE_NUMBER)
        Logger.logCallEvent("Call ended", phoneNumber)
        
        // End corresponding VoIP sessions
        serviceScope.launch {
            activeCallSessions.entries.removeAll { (sessionId, callEvent) ->
                if (callEvent.phoneNumber == phoneNumber) {
                    voipManager.endCall(sessionId)
                    
                    // Save call log
                    val finalEvent = callEvent.copy(
                        callState = CallState.ENDED,
                        duration = System.currentTimeMillis() - callEvent.timestamp.time
                    )
                    callLogRepository.insertCallLog(finalEvent)
                    
                    true
                } else {
                    false
                }
            }
        }
    }

    private suspend fun forwardCall(callEvent: CallEvent) {
        try {
            Logger.logCallEvent("Starting call forwarding", callEvent.phoneNumber)
            
            // Get forwarding destination from preferences
            val forwardingDestination = preferenceManager.getForwardingDestination()
            if (forwardingDestination.isNullOrEmpty()) {
                Logger.e(TAG, "No forwarding destination configured")
                return
            }
            
            // Update notification
            updateNotification("Forwarding call from ${callEvent.phoneNumber}")
            
            // Start VoIP session
            val voipSession = voipManager.startCall(forwardingDestination, callEvent.phoneNumber)
            if (voipSession != null) {
                val updatedEvent = callEvent.copy(
                    callState = CallState.FORWARDING,
                    isForwarded = true,
                    voipSessionId = voipSession.sessionId
                )
                
                activeCallSessions[voipSession.sessionId] = updatedEvent
                
                // Save to call log
                callLogRepository.insertCallLog(updatedEvent)
                
                Logger.logCallEvent("Call forwarding started", callEvent.phoneNumber, "VoIP Session: ${voipSession.sessionId}")
                
            } else {
                Logger.e(TAG, "Failed to start VoIP session for call forwarding")
                
                val failedEvent = callEvent.copy(
                    callState = CallState.FORWARD_FAILED,
                    errorMessage = "Failed to establish VoIP connection"
                )
                callLogRepository.insertCallLog(failedEvent)
            }
            
        } catch (e: Exception) {
            Logger.e(TAG, "Error during call forwarding", e)
            
            val errorEvent = callEvent.copy(
                callState = CallState.FORWARD_FAILED,
                errorMessage = e.message
            )
            callLogRepository.insertCallLog(errorEvent)
        }
    }

    private fun getVoIPConfiguration(): VoIPConfig {
        // Load VoIP configuration from preferences
        // In production, these should be securely stored and configurable via UI
        return VoIPConfig(
            serverAddress = preferenceManager.getSipServerAddress() ?: "sip.yourserver.com",
            serverPort = preferenceManager.getSipServerPort() ?: 5060,
            username = preferenceManager.getSipUsername() ?: "forwarding_user",
            password = preferenceManager.getSipPassword() ?: "secure_password",
            domain = preferenceManager.getSipDomain() ?: "yourserver.com"
        )
    }

    private fun createNotification(content: String): Notification {
        val intent = Intent(this, MainActivity::class.java)
        val pendingIntent = PendingIntent.getActivity(
            this, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, VoIPForwarderApplication.CALL_FORWARDING_CHANNEL_ID)
            .setContentTitle("VoIP Call Forwarder")
            .setContentText(content)
            .setSmallIcon(R.drawable.ic_phone_forwarded)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .build()
    }

    private fun updateNotification(content: String) {
        val notification = createNotification(content)
        val notificationManager = getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID, notification)
    }

    // Public methods for UI interaction
    fun isForwardingEnabled(): Boolean = isForwardingEnabled
    
    fun getActiveCallSessions(): Map<String, CallEvent> = activeCallSessions.toMap()
}
