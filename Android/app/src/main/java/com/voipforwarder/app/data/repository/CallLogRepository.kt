package com.voipforwarder.app.data.repository

import com.voipforwarder.app.data.database.CallLogDao
import com.voipforwarder.app.data.database.CallLogEntity
import com.voipforwarder.app.data.model.CallEvent
import com.voipforwarder.app.utils.Logger
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import java.util.Date

/**
 * Repository for managing call log data
 * Provides a clean API for accessing call log data and handles data mapping
 */
class CallLogRepository(private val callLogDao: CallLogDao) {

    companion object {
        private const val TAG = "CallLogRepository"
    }

    /**
     * Insert a call event into the database
     */
    suspend fun insertCallLog(callEvent: CallEvent): Long {
        Logger.d(TAG, "Inserting call log for ${callEvent.phoneNumber}")
        
        val entity = callEvent.toEntity()
        return callLogDao.insertCallLog(entity)
    }

    /**
     * Update an existing call log entry
     */
    suspend fun updateCallLog(callEvent: CallEvent, id: Long) {
        Logger.d(TAG, "Updating call log ID: $id")
        
        val entity = callEvent.toEntity().copy(id = id)
        callLogDao.updateCallLog(entity)
    }

    /**
     * Get all call logs as a flow
     */
    fun getAllCallLogs(): Flow<List<CallEvent>> {
        return callLogDao.getAllCallLogs().map { entities ->
            entities.map { it.toCallEvent() }
        }
    }

    /**
     * Get call logs with pagination
     */
    suspend fun getCallLogsPaged(limit: Int, offset: Int): List<CallEvent> {
        val entities = callLogDao.getCallLogsPaged(limit, offset)
        return entities.map { it.toCallEvent() }
    }

    /**
     * Get call logs by phone number
     */
    fun getCallLogsByNumber(phoneNumber: String): Flow<List<CallEvent>> {
        return callLogDao.getCallLogsByNumber(phoneNumber).map { entities ->
            entities.map { it.toCallEvent() }
        }
    }

    /**
     * Get only forwarded calls
     */
    fun getForwardedCalls(): Flow<List<CallEvent>> {
        return callLogDao.getForwardedCalls().map { entities ->
            entities.map { it.toCallEvent() }
        }
    }

    /**
     * Get failed forwarding attempts
     */
    fun getFailedForwardingAttempts(): Flow<List<CallEvent>> {
        return callLogDao.getFailedForwardingAttempts().map { entities ->
            entities.map { it.toCallEvent() }
        }
    }

    /**
     * Get call logs within a date range
     */
    fun getCallLogsInDateRange(startDate: Date, endDate: Date): Flow<List<CallEvent>> {
        return callLogDao.getCallLogsInDateRange(startDate, endDate).map { entities ->
            entities.map { it.toCallEvent() }
        }
    }

    /**
     * Get call log by VoIP session ID
     */
    suspend fun getCallLogBySessionId(sessionId: String): CallEvent? {
        val entity = callLogDao.getCallLogBySessionId(sessionId)
        return entity?.toCallEvent()
    }

    /**
     * Search call logs by phone number pattern
     */
    fun searchCallLogsByNumber(pattern: String): Flow<List<CallEvent>> {
        val searchPattern = "%$pattern%"
        return callLogDao.searchCallLogsByNumber(searchPattern).map { entities ->
            entities.map { it.toCallEvent() }
        }
    }

    /**
     * Get recent call logs (last 50)
     */
    fun getRecentCallLogs(): Flow<List<CallEvent>> {
        return callLogDao.getRecentCallLogs().map { entities ->
            entities.map { it.toCallEvent() }
        }
    }

    /**
     * Delete all call logs
     */
    suspend fun deleteAllCallLogs() {
        Logger.d(TAG, "Deleting all call logs")
        callLogDao.deleteAllCallLogs()
    }

    /**
     * Delete old call logs (older than specified date)
     */
    suspend fun deleteOldCallLogs(cutoffDate: Date): Int {
        Logger.d(TAG, "Deleting call logs older than $cutoffDate")
        return callLogDao.deleteOldCallLogs(cutoffDate)
    }

    /**
     * Get statistics - total forwarded calls
     */
    suspend fun getTotalForwardedCalls(): Int {
        return callLogDao.getTotalForwardedCalls()
    }

    /**
     * Get statistics - total failed calls
     */
    suspend fun getTotalFailedCalls(): Int {
        return callLogDao.getTotalFailedCalls()
    }

    /**
     * Get statistics - calls in the last 24 hours
     */
    suspend fun getCallsCountInLast24Hours(): Int {
        val yesterday = Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000)
        return callLogDao.getCallsCountSince(yesterday)
    }

    /**
     * Get average call duration for forwarded calls
     */
    suspend fun getAverageCallDuration(): Double {
        return callLogDao.getAverageCallDuration() ?: 0.0
    }

    /**
     * Clean up old logs (keep only last 30 days)
     */
    suspend fun cleanupOldLogs() {
        val thirtyDaysAgo = Date(System.currentTimeMillis() - 30 * 24 * 60 * 60 * 1000)
        val deletedCount = deleteOldCallLogs(thirtyDaysAgo)
        Logger.d(TAG, "Cleaned up $deletedCount old call logs")
    }

    // Extension functions for data mapping
    private fun CallEvent.toEntity(): CallLogEntity {
        return CallLogEntity(
            phoneNumber = this.phoneNumber,
            callState = this.callState,
            timestamp = this.timestamp,
            callDirection = this.callDirection,
            duration = this.duration,
            isForwarded = this.isForwarded,
            voipSessionId = this.voipSessionId,
            errorMessage = this.errorMessage
        )
    }

    private fun CallLogEntity.toCallEvent(): CallEvent {
        return CallEvent(
            phoneNumber = this.phoneNumber,
            callState = this.callState,
            timestamp = this.timestamp,
            callDirection = this.callDirection,
            duration = this.duration,
            isForwarded = this.isForwarded,
            voipSessionId = this.voipSessionId,
            errorMessage = this.errorMessage
        )
    }
}
