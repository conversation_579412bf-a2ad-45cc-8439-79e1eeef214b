package com.voipforwarder.app.ui

import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import com.voipforwarder.app.R

/**
 * Activity for configuring VoIP settings
 * TODO: Implement full configuration UI
 */
class ConfigurationActivity : AppCompatActivity() {
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // TODO: Create layout and implement configuration UI
        // This should include:
        // - SIP server settings
        // - Authentication credentials
        // - Forwarding destination
        // - Connection testing
        
        setContentView(R.layout.activity_main) // Placeholder
        
        supportActionBar?.setDisplayHomeAsUpEnabled(true)
        title = "VoIP Configuration"
    }
    
    override fun onSupportNavigateUp(): Boolean {
        onBackPressed()
        return true
    }
}
