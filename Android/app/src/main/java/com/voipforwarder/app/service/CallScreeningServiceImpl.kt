package com.voipforwarder.app.service

import android.os.Build
import android.telecom.Call
import android.telecom.CallScreeningService
import androidx.annotation.RequiresApi
import com.voipforwarder.app.data.model.CallEvent
import com.voipforwarder.app.data.model.CallState
import com.voipforwarder.app.utils.Logger
import com.voipforwarder.app.utils.PermissionUtils

/**
 * CallScreeningService implementation for Android 10+ (API 29+)
 * Provides more detailed call information and better call handling
 */
@RequiresApi(Build.VERSION_CODES.Q)
class CallScreeningServiceImpl : CallScreeningService() {

    companion object {
        private const val TAG = "CallScreeningService"
    }

    override fun onScreenCall(callDetails: Call.Details) {
        Logger.d(TAG, "Screening call: ${callDetails.handle}")

        // Check if we have required permissions
        if (!PermissionUtils.hasPhonePermissions(this)) {
            Logger.w(TAG, "Missing phone permissions, cannot screen call")
            return
        }

        val phoneNumber = callDetails.handle?.schemeSpecificPart
        val isIncoming = callDetails.callDirection == Call.Details.DIRECTION_INCOMING

        if (isIncoming && !phoneNumber.isNullOrEmpty()) {
            Logger.logCallEvent("Call screening - incoming call", phoneNumber)

            // Create call event
            val callEvent = CallEvent(
                phoneNumber = phoneNumber,
                callState = CallState.RINGING
            )

            // Notify call forwarding service
            val serviceIntent = android.content.Intent(this, CallForwardingService::class.java).apply {
                action = CallForwardingService.ACTION_INCOMING_CALL
                putExtra(CallForwardingService.EXTRA_CALL_EVENT, callEvent)
            }

            try {
                startForegroundService(serviceIntent)
                Logger.d(TAG, "Notified CallForwardingService of incoming call")
            } catch (e: Exception) {
                Logger.e(TAG, "Failed to notify CallForwardingService", e)
            }
        }

        // Allow the call to proceed normally
        // We don't want to block or reject calls, just forward them
        val response = CallResponse.Builder()
            .setDisallowCall(false)
            .setRejectCall(false)
            .setSkipCallLog(false)
            .setSkipNotification(false)
            .build()

        respondToCall(callDetails, response)
    }
}
