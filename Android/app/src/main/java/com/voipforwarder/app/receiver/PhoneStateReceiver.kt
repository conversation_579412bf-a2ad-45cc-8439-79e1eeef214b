package com.voipforwarder.app.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.telephony.TelephonyManager
import com.voipforwarder.app.data.model.CallEvent
import com.voipforwarder.app.data.model.CallState
import com.voipforwarder.app.service.CallForwardingService
import com.voipforwarder.app.utils.Logger
import com.voipforwarder.app.utils.PermissionUtils

/**
 * BroadcastReceiver that listens for phone state changes
 * Detects incoming calls and triggers call forwarding service
 */
class PhoneStateReceiver : BroadcastReceiver() {

    companion object {
        private const val TAG = "PhoneStateReceiver"
    }

    override fun onReceive(context: Context, intent: Intent) {
        Logger.d(TAG, "Received broadcast: ${intent.action}")

        if (intent.action != TelephonyManager.ACTION_PHONE_STATE_CHANGED) {
            return
        }

        // Check if we have required permissions
        if (!PermissionUtils.hasPhonePermissions(context)) {
            Logger.w(TAG, "Missing phone permissions, cannot process call state change")
            return
        }

        val state = intent.getStringExtra(TelephonyManager.EXTRA_STATE)
        val incomingNumber = intent.getStringExtra(TelephonyManager.EXTRA_INCOMING_NUMBER)

        Logger.logCallEvent("Phone state changed", incomingNumber, "State: $state")

        when (state) {
            TelephonyManager.EXTRA_STATE_RINGING -> {
                handleIncomingCall(context, incomingNumber)
            }
            TelephonyManager.EXTRA_STATE_OFFHOOK -> {
                handleCallAnswered(context, incomingNumber)
            }
            TelephonyManager.EXTRA_STATE_IDLE -> {
                handleCallEnded(context, incomingNumber)
            }
        }
    }

    private fun handleIncomingCall(context: Context, phoneNumber: String?) {
        if (phoneNumber.isNullOrEmpty()) {
            Logger.w(TAG, "Incoming call detected but no phone number available")
            return
        }

        Logger.logCallEvent("Incoming call detected", phoneNumber)

        val callEvent = CallEvent(
            phoneNumber = phoneNumber,
            callState = CallState.RINGING
        )

        // Start call forwarding service
        val serviceIntent = Intent(context, CallForwardingService::class.java).apply {
            action = CallForwardingService.ACTION_INCOMING_CALL
            putExtra(CallForwardingService.EXTRA_CALL_EVENT, callEvent)
        }

        try {
            context.startForegroundService(serviceIntent)
            Logger.d(TAG, "Started CallForwardingService for incoming call")
        } catch (e: Exception) {
            Logger.e(TAG, "Failed to start CallForwardingService", e)
        }
    }

    private fun handleCallAnswered(context: Context, phoneNumber: String?) {
        Logger.logCallEvent("Call answered", phoneNumber)

        val serviceIntent = Intent(context, CallForwardingService::class.java).apply {
            action = CallForwardingService.ACTION_CALL_ANSWERED
            phoneNumber?.let { putExtra(CallForwardingService.EXTRA_PHONE_NUMBER, it) }
        }

        try {
            context.startService(serviceIntent)
        } catch (e: Exception) {
            Logger.e(TAG, "Failed to notify service of call answered", e)
        }
    }

    private fun handleCallEnded(context: Context, phoneNumber: String?) {
        Logger.logCallEvent("Call ended", phoneNumber)

        val serviceIntent = Intent(context, CallForwardingService::class.java).apply {
            action = CallForwardingService.ACTION_CALL_ENDED
            phoneNumber?.let { putExtra(CallForwardingService.EXTRA_PHONE_NUMBER, it) }
        }

        try {
            context.startService(serviceIntent)
        } catch (e: Exception) {
            Logger.e(TAG, "Failed to notify service of call ended", e)
        }
    }
}
