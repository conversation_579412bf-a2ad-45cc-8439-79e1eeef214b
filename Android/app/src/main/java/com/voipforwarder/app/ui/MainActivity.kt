package com.voipforwarder.app.ui

import android.Manifest
import android.content.ComponentName
import android.content.Context
import android.content.Intent
import android.content.ServiceConnection
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.os.IBinder
import android.provider.Settings
import android.view.View
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.lifecycle.lifecycleScope
import com.voipforwarder.app.R
import com.voipforwarder.app.databinding.ActivityMainBinding
import com.voipforwarder.app.service.CallForwardingService
import com.voipforwarder.app.utils.Logger
import com.voipforwarder.app.utils.PermissionUtils
import com.voipforwarder.app.utils.PreferenceManager
import kotlinx.coroutines.launch

/**
 * Main activity for the VoIP Call Forwarder app
 * Provides UI controls for enabling/disabling call forwarding and configuration
 */
class MainActivity : AppCompatActivity() {

    companion object {
        private const val TAG = "MainActivity"
    }

    private lateinit var binding: ActivityMainBinding
    private lateinit var preferenceManager: PreferenceManager
    
    // Service connection
    private var callForwardingService: CallForwardingService? = null
    private var isServiceBound = false
    
    private val serviceConnection = object : ServiceConnection {
        override fun onServiceConnected(name: ComponentName?, service: IBinder?) {
            val binder = service as CallForwardingService.CallForwardingBinder
            callForwardingService = binder.getService()
            isServiceBound = true
            updateUI()
            Logger.d(TAG, "Service connected")
        }

        override fun onServiceDisconnected(name: ComponentName?) {
            callForwardingService = null
            isServiceBound = false
            Logger.d(TAG, "Service disconnected")
        }
    }

    // Permission launcher
    private val permissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.values.all { it }
        if (allGranted) {
            Logger.d(TAG, "All permissions granted")
            // Check if overlay permission is needed
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.canDrawOverlays(this)) {
                requestOverlayPermission()
            }
        } else {
            Logger.w(TAG, "Some permissions were denied")
            showPermissionDeniedDialog()
        }
    }

    // Overlay permission launcher
    private val overlayPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && Settings.canDrawOverlays(this)) {
            Logger.d(TAG, "Overlay permission granted")
        } else {
            Logger.w(TAG, "Overlay permission denied")
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        preferenceManager = PreferenceManager(this)
        
        setupUI()
        checkPermissions()
    }

    override fun onStart() {
        super.onStart()
        bindToService()
    }

    override fun onStop() {
        super.onStop()
        unbindFromService()
    }

    private fun setupUI() {
        // Set up toolbar
        setSupportActionBar(binding.toolbar)
        
        // Set up forwarding switch
        binding.switchForwarding.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                startCallForwarding()
            } else {
                stopCallForwarding()
            }
        }
        
        // Set up button listeners
        binding.btnConfiguration.setOnClickListener {
            startActivity(Intent(this, ConfigurationActivity::class.java))
        }
        
        binding.btnCallLogs.setOnClickListener {
            startActivity(Intent(this, CallLogsActivity::class.java))
        }
        
        binding.btnSettings.setOnClickListener {
            startActivity(Intent(this, SettingsActivity::class.java))
        }
        
        // Load initial state
        updateUI()
    }

    private fun checkPermissions() {
        val missingPermissions = PermissionUtils.getMissingPermissions(this)
        
        if (missingPermissions.isNotEmpty()) {
            Logger.d(TAG, "Missing permissions: ${missingPermissions.joinToString()}")
            showPermissionRationaleDialog(missingPermissions)
        } else {
            // Check overlay permission
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.canDrawOverlays(this)) {
                requestOverlayPermission()
            }
        }
    }

    private fun showPermissionRationaleDialog(permissions: List<String>) {
        AlertDialog.Builder(this)
            .setTitle(R.string.permission_required)
            .setMessage(R.string.phone_permission_rationale)
            .setPositiveButton(R.string.grant_permission) { _, _ ->
                permissionLauncher.launch(permissions.toTypedArray())
            }
            .setNegativeButton(R.string.cancel) { dialog, _ ->
                dialog.dismiss()
                showPermissionDeniedDialog()
            }
            .setCancelable(false)
            .show()
    }

    private fun showPermissionDeniedDialog() {
        AlertDialog.Builder(this)
            .setTitle(R.string.permission_required)
            .setMessage(R.string.permission_denied)
            .setPositiveButton(R.string.settings) { _, _ ->
                openAppSettings()
            }
            .setNegativeButton(R.string.ok) { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }

    private fun requestOverlayPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            val intent = Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION).apply {
                data = Uri.parse("package:$packageName")
            }
            overlayPermissionLauncher.launch(intent)
        }
    }

    private fun openAppSettings() {
        val intent = Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS).apply {
            data = Uri.parse("package:$packageName")
        }
        startActivity(intent)
    }

    private fun bindToService() {
        val intent = Intent(this, CallForwardingService::class.java)
        bindService(intent, serviceConnection, Context.BIND_AUTO_CREATE)
    }

    private fun unbindFromService() {
        if (isServiceBound) {
            unbindService(serviceConnection)
            isServiceBound = false
        }
    }

    private fun startCallForwarding() {
        Logger.d(TAG, "Starting call forwarding")
        
        // Check if configuration is complete
        if (!preferenceManager.hasSipConfiguration()) {
            showConfigurationRequiredDialog()
            binding.switchForwarding.isChecked = false
            return
        }
        
        // Check permissions
        if (!PermissionUtils.hasAllPermissions(this)) {
            checkPermissions()
            binding.switchForwarding.isChecked = false
            return
        }
        
        // Start the service
        val intent = Intent(this, CallForwardingService::class.java).apply {
            action = CallForwardingService.ACTION_START_FORWARDING
        }
        
        try {
            startForegroundService(intent)
            preferenceManager.setForwardingEnabled(true)
            updateUI()
        } catch (e: Exception) {
            Logger.e(TAG, "Failed to start call forwarding service", e)
            binding.switchForwarding.isChecked = false
        }
    }

    private fun stopCallForwarding() {
        Logger.d(TAG, "Stopping call forwarding")
        
        val intent = Intent(this, CallForwardingService::class.java).apply {
            action = CallForwardingService.ACTION_STOP_FORWARDING
        }
        
        try {
            startService(intent)
            preferenceManager.setForwardingEnabled(false)
            updateUI()
        } catch (e: Exception) {
            Logger.e(TAG, "Failed to stop call forwarding service", e)
        }
    }

    private fun showConfigurationRequiredDialog() {
        AlertDialog.Builder(this)
            .setTitle("Configuration Required")
            .setMessage("Please configure your VoIP settings before enabling call forwarding.")
            .setPositiveButton("Configure") { _, _ ->
                startActivity(Intent(this, ConfigurationActivity::class.java))
            }
            .setNegativeButton(R.string.cancel) { dialog, _ ->
                dialog.dismiss()
            }
            .show()
    }

    private fun updateUI() {
        lifecycleScope.launch {
            // Update forwarding status
            val isForwardingEnabled = preferenceManager.isForwardingEnabled()
            val isServiceRunning = callForwardingService?.isForwardingEnabled() ?: false
            
            binding.switchForwarding.isChecked = isForwardingEnabled && isServiceRunning
            
            // Update status indicator and text
            when {
                isServiceRunning -> {
                    binding.tvForwardingStatus.text = getString(R.string.status_active)
                    binding.tvStatusDetails.text = "Call forwarding is active and monitoring incoming calls"
                    binding.statusIndicator.setBackgroundColor(ContextCompat.getColor(this@MainActivity, R.color.status_active))
                }
                isForwardingEnabled -> {
                    binding.tvForwardingStatus.text = getString(R.string.status_connecting)
                    binding.tvStatusDetails.text = "Starting call forwarding service..."
                    binding.statusIndicator.setBackgroundColor(ContextCompat.getColor(this@MainActivity, R.color.status_connecting))
                }
                else -> {
                    binding.tvForwardingStatus.text = getString(R.string.status_inactive)
                    binding.tvStatusDetails.text = "Call forwarding is currently disabled"
                    binding.statusIndicator.setBackgroundColor(ContextCompat.getColor(this@MainActivity, R.color.status_inactive))
                }
            }
            
            // Update current call information
            val activeSessions = callForwardingService?.getActiveCallSessions() ?: emptyMap()
            if (activeSessions.isNotEmpty()) {
                val currentCall = activeSessions.values.first()
                binding.cardCurrentCall.visibility = View.VISIBLE
                binding.tvCurrentCallNumber.text = currentCall.phoneNumber
                binding.tvCurrentCallStatus.text = "Forwarding to ${preferenceManager.getForwardingDestination()}"
            } else {
                binding.cardCurrentCall.visibility = View.GONE
            }
            
            // Update statistics (placeholder - implement with actual data)
            binding.tvTotalForwarded.text = "0" // TODO: Load from database
            binding.tvTotalFailed.text = "0" // TODO: Load from database
        }
    }
}
