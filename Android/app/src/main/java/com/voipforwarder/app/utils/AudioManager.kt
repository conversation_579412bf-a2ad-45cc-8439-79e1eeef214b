package com.voipforwarder.app.utils

import android.content.Context
import android.media.AudioAttributes
import android.media.AudioFocusRequest
import android.media.AudioManager
import android.os.Build
import com.voipforwarder.app.utils.Logger

/**
 * Audio management utility for handling audio routing and focus during VoIP calls
 * Manages audio streams, routing, and system audio focus
 */
class AudioManager(private val context: Context) {

    companion object {
        private const val TAG = "AudioManager"
    }

    private val systemAudioManager = context.getSystemService(Context.AUDIO_SERVICE) as AudioManager
    private var audioFocusRequest: AudioFocusRequest? = null
    private var hasAudioFocus = false
    
    // Audio focus change listener
    private val audioFocusChangeListener = AudioManager.OnAudioFocusChangeListener { focusChange ->
        when (focusChange) {
            AudioManager.AUDIOFOCUS_GAIN -> {
                Logger.d(TAG, "Audio focus gained")
                hasAudioFocus = true
                onAudioFocusGained()
            }
            AudioManager.AUDIOFOCUS_LOSS -> {
                Logger.d(TAG, "Audio focus lost")
                hasAudioFocus = false
                onAudioFocusLost()
            }
            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT -> {
                Logger.d(TAG, "Audio focus lost temporarily")
                hasAudioFocus = false
                onAudioFocusLostTransient()
            }
            AudioManager.AUDIOFOCUS_LOSS_TRANSIENT_CAN_DUCK -> {
                Logger.d(TAG, "Audio focus lost - can duck")
                onAudioFocusLostCanDuck()
            }
        }
    }

    /**
     * Request audio focus for VoIP call
     */
    fun requestAudioFocus(): Boolean {
        Logger.d(TAG, "Requesting audio focus for VoIP call")
        
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val audioAttributes = AudioAttributes.Builder()
                .setUsage(AudioAttributes.USAGE_VOICE_COMMUNICATION)
                .setContentType(AudioAttributes.CONTENT_TYPE_SPEECH)
                .build()
                
            audioFocusRequest = AudioFocusRequest.Builder(AudioManager.AUDIOFOCUS_GAIN)
                .setAudioAttributes(audioAttributes)
                .setAcceptsDelayedFocusGain(true)
                .setOnAudioFocusChangeListener(audioFocusChangeListener)
                .build()
                
            val result = systemAudioManager.requestAudioFocus(audioFocusRequest!!)
            hasAudioFocus = result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED
            
            Logger.d(TAG, "Audio focus request result: $result")
            hasAudioFocus
        } else {
            @Suppress("DEPRECATION")
            val result = systemAudioManager.requestAudioFocus(
                audioFocusChangeListener,
                AudioManager.STREAM_VOICE_CALL,
                AudioManager.AUDIOFOCUS_GAIN
            )
            hasAudioFocus = result == AudioManager.AUDIOFOCUS_REQUEST_GRANTED
            
            Logger.d(TAG, "Audio focus request result (legacy): $result")
            hasAudioFocus
        }
    }

    /**
     * Release audio focus
     */
    fun releaseAudioFocus() {
        Logger.d(TAG, "Releasing audio focus")
        
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            audioFocusRequest?.let { request ->
                systemAudioManager.abandonAudioFocusRequest(request)
            }
        } else {
            @Suppress("DEPRECATION")
            systemAudioManager.abandonAudioFocus(audioFocusChangeListener)
        }
        
        hasAudioFocus = false
        audioFocusRequest = null
    }

    /**
     * Configure audio settings for VoIP call
     */
    fun configureForVoIPCall() {
        Logger.d(TAG, "Configuring audio for VoIP call")
        
        try {
            // Set audio mode to communication
            systemAudioManager.mode = AudioManager.MODE_IN_COMMUNICATION
            
            // Enable speaker phone if needed (can be made configurable)
            // systemAudioManager.isSpeakerphoneOn = false
            
            // Disable microphone mute
            systemAudioManager.isMicrophoneMute = false
            
            // Set stream volume for voice call
            val maxVolume = systemAudioManager.getStreamMaxVolume(AudioManager.STREAM_VOICE_CALL)
            val currentVolume = systemAudioManager.getStreamVolume(AudioManager.STREAM_VOICE_CALL)
            
            Logger.d(TAG, "Voice call volume: $currentVolume/$maxVolume")
            
            // Optionally adjust volume if too low
            if (currentVolume < maxVolume * 0.5) {
                systemAudioManager.setStreamVolume(
                    AudioManager.STREAM_VOICE_CALL,
                    (maxVolume * 0.7).toInt(),
                    0
                )
            }
            
        } catch (e: Exception) {
            Logger.e(TAG, "Error configuring audio for VoIP call", e)
        }
    }

    /**
     * Restore normal audio settings
     */
    fun restoreNormalAudio() {
        Logger.d(TAG, "Restoring normal audio settings")
        
        try {
            // Restore normal audio mode
            systemAudioManager.mode = AudioManager.MODE_NORMAL
            
            // Turn off speaker phone
            systemAudioManager.isSpeakerphoneOn = false
            
            // Release audio focus
            releaseAudioFocus()
            
        } catch (e: Exception) {
            Logger.e(TAG, "Error restoring normal audio", e)
        }
    }

    /**
     * Toggle speaker phone
     */
    fun toggleSpeakerPhone(): Boolean {
        val newState = !systemAudioManager.isSpeakerphoneOn
        systemAudioManager.isSpeakerphoneOn = newState
        Logger.d(TAG, "Speaker phone toggled to: $newState")
        return newState
    }

    /**
     * Toggle microphone mute
     */
    fun toggleMicrophone(): Boolean {
        val newState = !systemAudioManager.isMicrophoneMute
        systemAudioManager.isMicrophoneMute = newState
        Logger.d(TAG, "Microphone mute toggled to: $newState")
        return newState
    }

    /**
     * Check if speaker phone is on
     */
    fun isSpeakerPhoneOn(): Boolean {
        return systemAudioManager.isSpeakerphoneOn
    }

    /**
     * Check if microphone is muted
     */
    fun isMicrophoneMuted(): Boolean {
        return systemAudioManager.isMicrophoneMute
    }

    /**
     * Get current audio mode
     */
    fun getCurrentAudioMode(): Int {
        return systemAudioManager.mode
    }

    /**
     * Check if audio focus is currently held
     */
    fun hasAudioFocus(): Boolean {
        return hasAudioFocus
    }

    // Audio focus event handlers
    private fun onAudioFocusGained() {
        Logger.d(TAG, "Audio focus gained - resuming audio")
        // Resume audio processing if paused
    }

    private fun onAudioFocusLost() {
        Logger.d(TAG, "Audio focus lost - stopping audio")
        // Stop audio processing
        restoreNormalAudio()
    }

    private fun onAudioFocusLostTransient() {
        Logger.d(TAG, "Audio focus lost temporarily - pausing audio")
        // Pause audio processing temporarily
    }

    private fun onAudioFocusLostCanDuck() {
        Logger.d(TAG, "Audio focus lost - can duck - lowering volume")
        // Lower audio volume but continue playing
    }

    /**
     * Get audio routing information
     */
    fun getAudioRoutingInfo(): AudioRoutingInfo {
        return AudioRoutingInfo(
            isBluetoothConnected = systemAudioManager.isBluetoothA2dpOn,
            isWiredHeadsetConnected = systemAudioManager.isWiredHeadsetOn,
            isSpeakerPhoneOn = systemAudioManager.isSpeakerphoneOn,
            currentAudioMode = systemAudioManager.mode,
            hasAudioFocus = hasAudioFocus
        )
    }
}

/**
 * Data class containing audio routing information
 */
data class AudioRoutingInfo(
    val isBluetoothConnected: Boolean,
    val isWiredHeadsetConnected: Boolean,
    val isSpeakerPhoneOn: Boolean,
    val currentAudioMode: Int,
    val hasAudioFocus: Boolean
)
