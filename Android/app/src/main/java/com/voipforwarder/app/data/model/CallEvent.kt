package com.voipforwarder.app.data.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize
import java.util.Date

/**
 * Data class representing a call event
 */
@Parcelize
data class CallEvent(
    val phoneNumber: String,
    val callState: CallState,
    val timestamp: Date = Date(),
    val callDirection: CallDirection = CallDirection.INCOMING,
    val duration: Long = 0L, // Duration in milliseconds
    val isForwarded: Boolean = false,
    val voipSessionId: String? = null,
    val errorMessage: String? = null
) : Parcelable

/**
 * Enum representing different call states
 */
enum class CallState {
    IDLE,           // No call activity
    RINGING,        // Incoming call ringing
    OFFHOOK,        // Call answered or outgoing call
    ENDED,          // Call ended
    FORWARDING,     // Call being forwarded via VoIP
    FORWARDED,      // Call successfully forwarded
    FORWARD_FAILED  // Call forwarding failed
}

/**
 * Enum representing call direction
 */
enum class CallDirection {
    INCOMING,
    OUTGOING,
    MISSED
}

/**
 * Data class for VoIP session information
 */
@Parcelize
data class VoIPSession(
    val sessionId: String,
    val remoteAddress: String,
    val localAddress: String,
    val startTime: Date,
    val endTime: Date? = null,
    val status: VoIPSessionStatus,
    val errorMessage: String? = null
) : Parcelable

/**
 * Enum representing VoIP session status
 */
enum class VoIPSessionStatus {
    CONNECTING,
    CONNECTED,
    DISCONNECTED,
    FAILED,
    TIMEOUT
}
