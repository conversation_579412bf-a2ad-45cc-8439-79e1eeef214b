# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.

# Keep Linphone SDK classes
-keep class org.linphone.** { *; }
-keep class org.linphone.core.** { *; }

# Keep WebRTC classes (if using WebRTC instead)
# -keep class org.webrtc.** { *; }

# Keep Room database classes
-keep class * extends androidx.room.RoomDatabase
-keep @androidx.room.Entity class *
-dontwarn androidx.room.paging.**

# Keep Gson classes
-keepattributes Signature
-keepattributes *Annotation*
-dontwarn sun.misc.**
-keep class com.google.gson.** { *; }
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# Keep model classes for JSON serialization
-keep class com.voipforwarder.app.data.model.** { *; }

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}
