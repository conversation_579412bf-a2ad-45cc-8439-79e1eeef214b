# VoIP Call Forwarder for Android

A comprehensive Android application that detects incoming calls on a SIM-enabled device and forwards them via VoIP to another device (like an iPhone without SIM). Built with Kotlin and modern Android architecture components.

## Features

- **Automatic Call Detection**: Uses TelephonyManager and CallScreeningService to detect incoming calls
- **VoIP Integration**: Supports SIP/WebRTC protocols via Linphone SDK
- **Background Operation**: Runs as a foreground service to ensure continuous operation
- **Audio Management**: Handles audio routing and management during call forwarding
- **Call Logging**: Stores forwarded call logs locally with Room database
- **Error Handling**: Comprehensive error handling with retry mechanisms
- **Modern UI**: Material Design 3 interface with real-time status updates

## Architecture

The app follows modern Android architecture patterns:

- **MVVM Architecture**: Clean separation of concerns
- **Repository Pattern**: Centralized data access
- **Room Database**: Local storage for call logs
- **Coroutines**: Asynchronous operations
- **Foreground Services**: Background call monitoring
- **Dependency Injection**: Modular and testable code

## Project Structure

```
app/src/main/java/com/voipforwarder/app/
├── data/
│   ├── database/          # Room database entities and DAOs
│   ├── model/            # Data models and enums
│   └── repository/       # Data repositories
├── receiver/             # Broadcast receivers for call detection
├── service/              # Background services
├── ui/                   # Activities and UI components
├── utils/                # Utility classes
├── voip/                 # VoIP integration layer
└── VoIPForwarderApplication.kt
```

## Required Permissions

The app requires the following permissions:

- `READ_PHONE_STATE` - Detect incoming calls
- `READ_CALL_LOG` - Access call information
- `ANSWER_PHONE_CALLS` - Handle call operations
- `RECORD_AUDIO` - Audio processing during calls
- `MODIFY_AUDIO_SETTINGS` - Audio routing management
- `MANAGE_OWN_CALLS` - Call management
- `CALL_PHONE` - Outgoing call capabilities
- `INTERNET` - VoIP connectivity
- `FOREGROUND_SERVICE` - Background operation
- `SYSTEM_ALERT_WINDOW` - Call overlay (optional)

## Backend Server Setup

### SIP Server Configuration

You'll need a SIP server to handle VoIP calls. Popular options include:

1. **Asterisk** (Open Source)
2. **FreeSWITCH** (Open Source)
3. **Kamailio** (Open Source)
4. **Commercial SIP providers** (Twilio, etc.)

### Example Asterisk Configuration

```ini
# /etc/asterisk/sip.conf
[general]
context=default
allowoverlap=no
bindport=5060
bindaddr=0.0.0.0
srvlookup=yes

[forwarding_user_001]
type=friend
secret=secure_password_123
host=dynamic
context=default
canreinvite=no
```

### Example Extensions Configuration

```ini
# /etc/asterisk/extensions.conf
[default]
exten => _X.,1,Dial(SIP/destination_user)
exten => _X.,n,Hangup()
```

## VoIP Integration

### Linphone SDK Integration

The app uses Linphone SDK for VoIP functionality. To integrate:

1. **Add Dependency** (already included in build.gradle):
```kotlin
implementation 'org.linphone:linphone-android:5.2.0'
```

2. **Initialize Core**:
```kotlin
val factory = Factory.instance()
val core = factory.createCore(null, null, context)
```

3. **Configure SIP Account**:
```kotlin
val proxyConfig = core.createProxyConfig()
val identity = "sip:<EMAIL>"
val address = "sip:server.com:5060"

proxyConfig.identityAddress = core.interpretUrl(identity)
proxyConfig.serverAddr = address
proxyConfig.registerEnabled = true

core.addProxyConfig(proxyConfig)
core.defaultProxyConfig = proxyConfig
```

### Alternative: WebRTC Integration

For WebRTC instead of SIP, replace Linphone dependency with:

```kotlin
implementation 'org.webrtc:google-webrtc:1.0.32006'
```

## Configuration

### VoIP Server Settings

Configure these settings in the app:

- **Server Address**: `sip.yourserver.com`
- **Port**: `5060` (default SIP port)
- **Username**: Your SIP account username
- **Password**: Your SIP account password
- **Domain**: Your SIP domain
- **Forwarding Destination**: Target SIP address

### Example Configuration

```kotlin
val config = VoIPConfig(
    serverAddress = "sip.yourserver.com",
    serverPort = 5060,
    username = "forwarding_user_001",
    password = "secure_password_123",
    domain = "yourserver.com"
)
```

## Installation and Setup

1. **Clone the repository**:
```bash
git clone <repository-url>
cd VoIPCallForwarder
```

2. **Open in Android Studio**:
   - Import the project
   - Sync Gradle files
   - Build the project

3. **Configure VoIP Settings**:
   - Set up your SIP server
   - Configure server details in the app
   - Set forwarding destination

4. **Grant Permissions**:
   - Install the app
   - Grant all required permissions
   - Enable system overlay permission if needed

5. **Enable Call Forwarding**:
   - Open the app
   - Toggle call forwarding on
   - The service will start monitoring calls

## Usage

1. **Initial Setup**:
   - Configure VoIP server settings
   - Set forwarding destination
   - Grant all required permissions

2. **Enable Forwarding**:
   - Toggle the forwarding switch in the main screen
   - The app will start monitoring incoming calls

3. **Monitor Status**:
   - Check the status indicator for connection state
   - View active call information
   - Review call logs and statistics

## Testing

### Unit Tests

Run unit tests:
```bash
./gradlew test
```

### Integration Tests

Run instrumented tests:
```bash
./gradlew connectedAndroidTest
```

### Manual Testing

1. **Call Detection**: Make a test call to verify detection
2. **VoIP Connection**: Test SIP server connectivity
3. **Audio Routing**: Verify audio forwarding works
4. **Background Operation**: Test with app in background
5. **Error Handling**: Test with network issues

## Troubleshooting

### Common Issues

1. **Permissions Denied**:
   - Ensure all permissions are granted
   - Check system overlay permission

2. **VoIP Connection Failed**:
   - Verify server settings
   - Check network connectivity
   - Validate SIP credentials

3. **Calls Not Detected**:
   - Verify phone permissions
   - Check if CallScreeningService is enabled
   - Test with different call sources

4. **Audio Issues**:
   - Check microphone permissions
   - Verify audio routing settings
   - Test with different audio devices

### Debug Logs

Enable debug logging to troubleshoot issues:

```kotlin
// In VoIPForwarderApplication.kt
if (BuildConfig.DEBUG) {
    Timber.plant(Timber.DebugTree())
}
```

View logs with:
```bash
adb logcat -s VoIPForwarder
```

## Security Considerations

- **Credentials Storage**: SIP credentials are stored using EncryptedSharedPreferences
- **Network Security**: Use TLS for SIP connections in production
- **Permissions**: Request only necessary permissions
- **Data Privacy**: Call logs are stored locally only

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Disclaimer

This app is for educational and legitimate use only. Ensure compliance with local laws and regulations regarding call forwarding and VoIP services. The developers are not responsible for any misuse of this application.

## Support

For issues and questions:
- Create an issue on GitHub
- Check the troubleshooting section
- Review the documentation

## Roadmap

- [ ] Enhanced UI for configuration screens
- [ ] Support for multiple SIP accounts
- [ ] Call recording functionality
- [ ] Push notification integration
- [ ] Cloud backup for call logs
- [ ] Advanced audio codec support
- [ ] Video call forwarding support
