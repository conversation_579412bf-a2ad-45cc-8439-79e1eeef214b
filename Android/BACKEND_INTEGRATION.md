# Backend Server Integration Guide

This guide explains how to set up and integrate a backend SIP server with the VoIP Call Forwarder Android app.

## Overview

The Android app forwards incoming calls to a SIP server, which then routes them to the destination device. You'll need:

1. **SIP Server** - Handles VoIP calls and routing
2. **Network Configuration** - Proper firewall and NAT settings
3. **SIP Accounts** - User accounts for authentication
4. **Destination Setup** - Target device configuration

## SIP Server Options

### 1. Asterisk (Recommended for beginners)

**Installation on Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install asterisk
```

**Basic Configuration:**

`/etc/asterisk/sip.conf`:
```ini
[general]
context=default
allowoverlap=no
bindport=5060
bindaddr=0.0.0.0
srvlookup=yes
disallow=all
allow=ulaw
allow=alaw
allow=g722

; Android app account
[android_forwarder]
type=friend
secret=your_secure_password_here
host=dynamic
context=incoming_calls
canreinvite=no
qualify=yes

; Destination device account (iPhone, etc.)
[destination_device]
type=friend
secret=another_secure_password
host=dynamic
context=default
canreinvite=no
qualify=yes
```

`/etc/asterisk/extensions.conf`:
```ini
[incoming_calls]
; Forward all incoming calls to destination device
exten => _X.,1,Dial(SIP/destination_device,30)
exten => _X.,n,Voicemail(destination_device@default)
exten => _X.,n,Hangup()

[default]
; Handle calls from destination device
exten => _X.,1,Dial(SIP/${EXTEN})
exten => _X.,n,Hangup()
```

### 2. FreeSWITCH (Advanced users)

**Installation:**
```bash
wget -O - https://files.freeswitch.org/repo/deb/debian-release/fsstretch-archive-keyring.asc | apt-key add -
echo "deb http://files.freeswitch.org/repo/deb/debian-release/ stretch main" > /etc/apt/sources.list.d/freeswitch.list
apt-get update
apt-get install freeswitch-meta-all
```

**Configuration:**

`/etc/freeswitch/directory/default/android_forwarder.xml`:
```xml
<include>
  <user id="android_forwarder">
    <params>
      <param name="password" value="your_secure_password"/>
      <param name="vm-password" value="1234"/>
    </params>
    <variables>
      <variable name="toll_allow" value="domestic,international,local"/>
      <variable name="accountcode" value="android_forwarder"/>
      <variable name="user_context" value="default"/>
      <variable name="effective_caller_id_name" value="Android Forwarder"/>
      <variable name="effective_caller_id_number" value="1000"/>
    </variables>
  </user>
</include>
```

### 3. Cloud SIP Providers

For production use, consider cloud providers:

- **Twilio**: Enterprise-grade with APIs
- **Vonage**: Global coverage
- **Bandwidth**: Carrier-grade infrastructure
- **SignalWire**: Modern cloud communications

## Network Configuration

### Firewall Settings

Open required ports:
```bash
# SIP signaling
sudo ufw allow 5060/udp
sudo ufw allow 5060/tcp

# RTP media (adjust range as needed)
sudo ufw allow 10000:20000/udp
```

### NAT Configuration

For servers behind NAT, configure port forwarding:
- **5060 UDP/TCP** → SIP Server IP:5060
- **10000-20000 UDP** → SIP Server IP:10000-20000

### STUN/TURN Server

For NAT traversal, set up a STUN server:

```bash
sudo apt install coturn
```

`/etc/turnserver.conf`:
```ini
listening-port=3478
fingerprint
lt-cred-mech
use-auth-secret
static-auth-secret=your_secret_key
realm=yourdomain.com
total-quota=100
stale-nonce=600
cert=/etc/ssl/certs/yourdomain.com.crt
pkey=/etc/ssl/private/yourdomain.com.key
```

## Android App Configuration

### VoIP Settings

Configure these in the Android app:

```kotlin
val config = VoIPConfig(
    serverAddress = "your-server.com",  // Your SIP server IP/domain
    serverPort = 5060,                  // SIP port
    username = "android_forwarder",     // SIP account username
    password = "your_secure_password",  // SIP account password
    domain = "your-server.com",         // SIP domain
    transport = TransportType.UDP,      // Transport protocol
    enableStun = true,                  // Enable STUN for NAT
    stunServer = "stun.your-server.com:3478"
)
```

### Forwarding Destination

Set the destination where calls should be forwarded:
```
<EMAIL>
```

## Destination Device Setup

### iPhone Configuration

1. **Install a SIP app** (Linphone, Zoiper, etc.)
2. **Configure SIP account**:
   - Server: `your-server.com`
   - Username: `destination_device`
   - Password: `another_secure_password`
   - Domain: `your-server.com`

### Android Destination Device

Use the same SIP client apps or configure built-in SIP support.

## Testing the Setup

### 1. Test SIP Registration

Check if devices register successfully:
```bash
# Asterisk
asterisk -r
sip show peers

# FreeSWITCH
fs_cli
sofia status profile internal reg
```

### 2. Test Call Flow

1. **Make a test call** to the Android device
2. **Verify call detection** in app logs
3. **Check call forwarding** to destination
4. **Confirm audio quality** on destination device

### 3. Debug Common Issues

**Registration Failures:**
```bash
# Check Asterisk logs
tail -f /var/log/asterisk/messages

# Check FreeSWITCH logs
tail -f /var/log/freeswitch/freeswitch.log
```

**Audio Issues:**
- Verify RTP ports are open
- Check NAT configuration
- Test with different codecs

## Security Best Practices

### 1. Authentication

- Use strong passwords (20+ characters)
- Enable SIP digest authentication
- Consider certificate-based auth

### 2. Encryption

Enable TLS for SIP signaling:

`/etc/asterisk/sip.conf`:
```ini
[general]
tlsenable=yes
tlsbindaddr=0.0.0.0:5061
tlscertfile=/etc/asterisk/keys/asterisk.crt
tlsprivatekey=/etc/asterisk/keys/asterisk.key
```

### 3. Access Control

Restrict access by IP:
```ini
[android_forwarder]
type=friend
secret=password
host=dynamic
permit=***********/*************
deny=0.0.0.0/0.0.0.0
```

### 4. Rate Limiting

Prevent abuse with fail2ban:

`/etc/fail2ban/jail.local`:
```ini
[asterisk]
enabled = true
port = 5060
protocol = udp
filter = asterisk
logpath = /var/log/asterisk/messages
maxretry = 5
bantime = 3600
```

## Monitoring and Maintenance

### 1. Log Monitoring

Set up log rotation and monitoring:
```bash
# Asterisk logs
logrotate /etc/logrotate.d/asterisk

# Monitor for errors
tail -f /var/log/asterisk/messages | grep ERROR
```

### 2. Performance Monitoring

Monitor server resources:
```bash
# CPU and memory usage
htop

# Network connections
netstat -an | grep :5060

# Asterisk-specific monitoring
asterisk -r
core show channels
sip show channelstats
```

### 3. Backup Configuration

Regular backups of configuration:
```bash
# Asterisk
tar -czf asterisk-config-$(date +%Y%m%d).tar.gz /etc/asterisk/

# FreeSWITCH
tar -czf freeswitch-config-$(date +%Y%m%d).tar.gz /etc/freeswitch/
```

## Troubleshooting

### Common Issues

1. **Registration Timeout**
   - Check network connectivity
   - Verify server address and port
   - Check firewall settings

2. **Authentication Failed**
   - Verify username/password
   - Check SIP account configuration
   - Review server logs

3. **No Audio**
   - Check RTP port range
   - Verify NAT configuration
   - Test with different codecs

4. **Call Not Forwarded**
   - Check dialplan configuration
   - Verify destination account
   - Review call routing rules

### Debug Commands

**Asterisk:**
```bash
asterisk -r
sip set debug on
core set verbose 5
core set debug 5
```

**FreeSWITCH:**
```bash
fs_cli
sofia loglevel all 9
sofia global siptrace on
```

## Production Deployment

### 1. High Availability

- Set up multiple SIP servers
- Use load balancers
- Implement failover mechanisms

### 2. Scalability

- Monitor concurrent calls
- Scale server resources
- Consider clustering solutions

### 3. Compliance

- Ensure regulatory compliance
- Implement call recording if required
- Maintain audit logs

## Support and Resources

- **Asterisk Documentation**: https://wiki.asterisk.org/
- **FreeSWITCH Documentation**: https://freeswitch.org/confluence/
- **SIP RFC 3261**: https://tools.ietf.org/html/rfc3261
- **Community Forums**: Various SIP and VoIP communities

For specific integration issues, refer to the main README.md troubleshooting section.
