# User Guide - VoIP Call Forwarding System

## 📱 Overview

The VoIP Call Forwarding System allows you to receive calls on your iPhone even when it doesn't have a SIM card. Calls are forwarded from your Android phone to your iPhone over the internet, providing a seamless calling experience.

## 🚀 Quick Setup Guide

### Prerequisites
- **Android phone** with active SIM card and internet connection
- **iPhone** with internet connection (WiFi or cellular data)
- Both devices connected to the internet
- Backend service running and accessible

### Step 1: Install Applications

#### Android App Installation
1. Download the VoIP Forwarder APK from the provided source
2. Enable "Install from Unknown Sources" in Android Settings
3. Install the APK file
4. Grant required permissions when prompted

#### iOS App Installation
1. Install the VoIP Receiver app from the App Store (or TestFlight for beta)
2. Grant microphone and notification permissions when prompted
3. The app will automatically configure VoIP capabilities

### Step 2: Configure Backend Connection

#### Android App Configuration
1. Open the VoIP Forwarder app
2. Go to **Settings** → **Server Configuration**
3. Enter the backend server URL (e.g., `https://your-server.com`)
4. Test the connection using the "Test Connection" button
5. Save the configuration

#### iOS App Configuration
1. Open the VoIP Receiver app
2. Navigate to **Settings** → **Server Settings**
3. Enter the same backend server URL
4. Verify the connection status shows "Connected"

### Step 3: Device Registration

#### Register Android Device
1. In the Android app, go to **Settings** → **Device Registration**
2. Enter a unique device name (e.g., "John's Android")
3. The app will automatically generate a device ID
4. Tap **Register Device**
5. Wait for confirmation message

#### Register iOS Device
1. In the iOS app, go to **Settings** → **Device Registration**
2. Enter a unique device name (e.g., "John's iPhone")
3. The app will automatically:
   - Generate a device ID
   - Register for VoIP push notifications
   - Configure CallKit integration
4. Tap **Register Device**
5. Allow push notification permissions when prompted

### Step 4: Pair Devices

#### From Android App
1. Go to **Settings** → **Device Pairing**
2. Tap **Scan for iOS Devices** or **Enter Device ID Manually**
3. Select your iOS device from the list
4. Tap **Pair Devices**
5. Wait for pairing confirmation

#### Verify Pairing on iOS
1. Open the iOS app
2. Check **Settings** → **Paired Devices**
3. You should see your Android device listed
4. Status should show "Connected"

## 📞 Using the System

### Making/Receiving Calls

#### Normal Call Flow
1. **Incoming call on Android**: When someone calls your Android phone
2. **Automatic detection**: The Android app detects the incoming call
3. **Call forwarding**: The call is automatically forwarded to your iPhone
4. **iPhone notification**: Your iPhone receives a VoIP push notification
5. **Native call interface**: The call appears as a normal iPhone call with CallKit
6. **Answer on iPhone**: Answer the call using the standard iPhone interface

#### Call Controls
- **Answer**: Swipe right or tap the green button
- **Decline**: Swipe left or tap the red button
- **Hold**: Tap the hold button during the call
- **Mute**: Tap the mute button to mute your microphone
- **Speaker**: Tap the speaker button for hands-free mode
- **End call**: Tap the red end call button

### Call History

#### Viewing Call History
1. Open either the Android or iOS app
2. Navigate to the **Call History** tab
3. View detailed information about each call:
   - Caller name and number
   - Call duration
   - Call quality metrics
   - Date and time

#### Call History Features
- **Search**: Search by caller name or number
- **Filter**: Filter by date range, call type, or duration
- **Export**: Export call history to CSV or PDF
- **Details**: Tap any call for detailed information

## ⚙️ Settings and Configuration

### Android App Settings

#### General Settings
- **Device Name**: Change your device display name
- **Auto-Forward**: Enable/disable automatic call forwarding
- **Call Screening**: Configure which calls to forward
- **Notification Settings**: Customize notification preferences

#### Advanced Settings
- **SIP Configuration**: Configure SIP server settings
- **Audio Quality**: Adjust audio codec and quality settings
- **Network Settings**: Configure network preferences
- **Debug Mode**: Enable detailed logging for troubleshooting

#### Call Forwarding Rules
1. Go to **Settings** → **Forwarding Rules**
2. Configure rules based on:
   - Contact groups
   - Phone number patterns
   - Time of day
   - Call type (voice, video)

### iOS App Settings

#### General Settings
- **Device Name**: Change your device display name
- **Call Notifications**: Configure notification preferences
- **Audio Settings**: Adjust audio routing and quality
- **Background Mode**: Ensure background operation is enabled

#### CallKit Configuration
- **Caller ID Display**: Configure how caller information is shown
- **Ringtone**: Select custom ringtones for forwarded calls
- **Call Blocking**: Configure call blocking rules
- **Integration**: Enable/disable system integration features

#### Push Notification Settings
- **VoIP Notifications**: Ensure VoIP push notifications are enabled
- **Badge Count**: Configure app badge behavior
- **Sound Settings**: Customize notification sounds
- **Do Not Disturb**: Configure behavior during Do Not Disturb mode

## 🔧 Troubleshooting

### Common Issues and Solutions

#### Calls Not Being Forwarded

**Symptoms**: Incoming calls on Android are not forwarded to iPhone

**Solutions**:
1. **Check internet connection** on both devices
2. **Verify device pairing** in both apps
3. **Restart both applications**
4. **Check server connection** in settings
5. **Verify permissions** are granted on Android

#### iPhone Not Receiving Calls

**Symptoms**: Android forwards calls but iPhone doesn't ring

**Solutions**:
1. **Check push notification permissions** on iPhone
2. **Verify VoIP push notifications** are enabled
3. **Restart the iOS app**
4. **Check Do Not Disturb settings**
5. **Re-register the iOS device**

#### Poor Call Quality

**Symptoms**: Audio is choppy, delayed, or unclear

**Solutions**:
1. **Check internet connection speed** on both devices
2. **Switch to WiFi** if using cellular data
3. **Close other apps** using network bandwidth
4. **Adjust audio quality settings** in both apps
5. **Check for network interference**

#### Battery Drain Issues

**Symptoms**: Excessive battery usage on either device

**Solutions**:
1. **Optimize background settings** in both apps
2. **Disable unnecessary features** like video calling
3. **Use WiFi instead of cellular** when possible
4. **Update to latest app versions**
5. **Check for background app refresh settings**

### Advanced Troubleshooting

#### Network Connectivity Issues
1. **Test server connectivity**:
   - Use the built-in connection test in both apps
   - Check if the server URL is correct and accessible
   - Verify firewall settings don't block the connection

2. **WebRTC connection problems**:
   - Check if STUN/TURN servers are configured
   - Verify NAT traversal is working
   - Test with different network connections

3. **Push notification delivery**:
   - Check Apple Push Notification service status
   - Verify push certificates are valid
   - Test push notification delivery manually

#### Audio Issues
1. **Microphone problems**:
   - Check microphone permissions on both devices
   - Test microphone in other apps
   - Verify audio session configuration

2. **Speaker/earpiece issues**:
   - Test audio routing options
   - Check Bluetooth connectivity
   - Verify audio session category settings

3. **Echo or feedback**:
   - Enable echo cancellation in settings
   - Adjust microphone sensitivity
   - Use headphones to eliminate feedback

## 📊 Monitoring and Analytics

### Call Quality Monitoring

#### Real-time Metrics
Both apps display real-time call quality information:
- **Connection strength**: Signal strength indicator
- **Audio quality**: Real-time audio quality meter
- **Network latency**: Round-trip time display
- **Packet loss**: Network packet loss percentage

#### Historical Analytics
View detailed analytics in the **Analytics** section:
- **Call success rate**: Percentage of successful calls
- **Average call duration**: Mean call length over time
- **Quality trends**: Call quality trends over time
- **Network performance**: Network performance statistics

### Usage Statistics
- **Daily/weekly/monthly call volume**
- **Peak usage times**
- **Most frequent callers**
- **Geographic call distribution** (if location services enabled)

## 🔒 Privacy and Security

### Data Protection
- **End-to-end encryption**: All calls use WebRTC encryption
- **Secure authentication**: JWT-based secure authentication
- **Local data storage**: Call history stored locally on devices
- **No call recording**: Calls are not recorded by default

### Privacy Settings
1. **Call history retention**: Configure how long to keep call history
2. **Analytics sharing**: Choose whether to share anonymous usage data
3. **Location services**: Control location-based features
4. **Contact access**: Manage contact information access

### Security Best Practices
- **Keep apps updated**: Install updates promptly for security patches
- **Use strong passwords**: If SIP authentication is required
- **Secure network connections**: Use trusted WiFi networks
- **Regular security reviews**: Periodically review app permissions

## 🆘 Getting Help

### In-App Support
- **Help section**: Access built-in help and tutorials
- **Diagnostic tools**: Use built-in diagnostic features
- **Log sharing**: Share diagnostic logs with support team

### Contact Support
- **Email support**: <EMAIL>
- **Online documentation**: Visit the project documentation
- **Community forum**: Join the user community for peer support
- **GitHub issues**: Report bugs and feature requests

### Self-Help Resources
- **FAQ section**: Common questions and answers
- **Video tutorials**: Step-by-step video guides
- **User manual**: Comprehensive user documentation
- **Troubleshooting guide**: Detailed problem-solving steps

---

**Need more help?** Check out our [Troubleshooting Guide](TROUBLESHOOTING.md) or contact our support team.
