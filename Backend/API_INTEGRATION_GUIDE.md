# VoIP Backend API Integration Guide

This guide provides detailed instructions for integrating with the VoIP Backend API from both Android and iOS applications.

## Authentication Flow

### 1. Device Registration

First, register your device with the backend:

```bash
POST /api/v1/devices/register
Content-Type: application/json

{
  "deviceId": "android-device-123",
  "deviceType": "android",
  "deviceName": "Samsung Galaxy S21",
  "osVersion": "Android 12",
  "appVersion": "1.0.0",
  "pushToken": "fcm-token-here",
  "sipUsername": "user123",
  "sipPassword": "secure-password",
  "sipDomain": "voip.example.com",
  "capabilities": {
    "supportsVideo": false,
    "supportedCodecs": ["PCMU", "PCMA", "opus"],
    "maxBitrate": 64000,
    "supportsHold": true,
    "supportsTransfer": false
  },
  "networkInfo": {
    "ipAddress": "*************",
    "userAgent": "VoIP-Android/1.0",
    "connectionType": "wifi"
  }
}
```

### 2. Authentication

Authenticate to get JWT tokens:

```bash
POST /api/v1/auth/authenticate
Content-Type: application/json

{
  "deviceId": "android-device-123",
  "sipUsername": "user123",
  "sipPassword": "secure-password"
}
```

Response:
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "device": {
    "id": "uuid-here",
    "deviceId": "android-device-123",
    "deviceType": "android",
    "status": "active",
    "isPaired": false
  },
  "expires_in": 86400
}
```

### 3. Using JWT Tokens

Include the access token in all subsequent requests:

```bash
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Device Pairing

### Pair Android and iOS Devices

```bash
POST /api/v1/devices/pair
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
  "androidDeviceId": "android-device-123",
  "iosDeviceId": "ios-device-456"
}
```

Response:
```json
{
  "android": {
    "id": "uuid-1",
    "deviceId": "android-device-123",
    "pairedDeviceId": "uuid-2"
  },
  "ios": {
    "id": "uuid-2", 
    "deviceId": "ios-device-456",
    "pairedDeviceId": "uuid-1"
  }
}
```

## Call Management

### 1. Initiate Call (Android to iOS)

```bash
POST /api/v1/calls/initiate
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
  "androidDeviceId": "android-device-123",
  "callerId": "+1234567890",
  "callerName": "John Doe"
}
```

Response:
```json
{
  "id": "call-uuid",
  "callId": "unique-call-id",
  "status": "ringing",
  "startTime": "2023-12-07T10:30:00Z",
  "callerId": "+1234567890",
  "callerName": "John Doe"
}
```

### 2. Answer Call (iOS)

```bash
POST /api/v1/calls/{callId}/answer
Authorization: Bearer YOUR_JWT_TOKEN
```

### 3. Update Call Quality

```bash
PUT /api/v1/calls/{callId}
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
  "qualityMetrics": {
    "jitter": 12.5,
    "packetLoss": 0.1,
    "roundTripTime": 45,
    "audioLevel": -20,
    "qualityScore": "good"
  },
  "networkStats": {
    "bytesReceived": 1024000,
    "bytesSent": 1024000,
    "packetsReceived": 1000,
    "packetsSent": 1000,
    "packetsLost": 1
  }
}
```

### 4. End Call

```bash
POST /api/v1/calls/{callId}/end
Authorization: Bearer YOUR_JWT_TOKEN
Content-Type: application/json

{
  "reason": "normal"
}
```

## WebSocket Integration

### Connection

Connect to the WebSocket gateway for real-time communication:

```javascript
import io from 'socket.io-client';

const socket = io('http://localhost:3000/webrtc', {
  auth: {
    token: 'YOUR_JWT_TOKEN'
  }
});

socket.on('connect', () => {
  console.log('Connected to WebSocket');
});

socket.on('disconnect', () => {
  console.log('Disconnected from WebSocket');
});
```

### WebRTC Signaling

```javascript
// Send WebRTC offer
socket.emit('webrtc-signal', {
  type: 'offer',
  callId: 'call-uuid',
  fromDevice: 'android-device-123',
  toDevice: 'ios-device-456',
  data: {
    sdp: 'v=0\r\no=- 123456789 123456789 IN IP4 *************\r\n...'
  }
});

// Listen for WebRTC signals
socket.on('webrtc-signal', (message) => {
  console.log('Received WebRTC signal:', message);
  
  switch (message.type) {
    case 'offer':
      // Handle incoming offer
      handleOffer(message.data.sdp);
      break;
    case 'answer':
      // Handle answer
      handleAnswer(message.data.sdp);
      break;
    case 'ice-candidate':
      // Handle ICE candidate
      handleIceCandidate(message.data.candidate);
      break;
  }
});
```

### Call Events

```javascript
// Initiate call
socket.emit('initiate-call', {
  callerId: '+1234567890',
  callerName: 'John Doe'
});

// Listen for call events
socket.on('call-initiated', (data) => {
  console.log('Call initiated:', data);
});

socket.on('call-answered', (data) => {
  console.log('Call answered:', data);
});

socket.on('call-ended', (data) => {
  console.log('Call ended:', data);
});

// Answer call
socket.emit('answer-call', {
  callId: 'call-uuid'
});

// End call
socket.emit('end-call', {
  callId: 'call-uuid',
  reason: 'normal'
});
```

## Push Notifications (iOS)

### VoIP Push Notification Payload

When a call is initiated, the iOS device will receive a VoIP push notification:

```json
{
  "caller_id": "+1234567890",
  "caller_name": "John Doe",
  "call_uuid": "unique-call-uuid",
  "server_info": {
    "sip_server": "voip.example.com",
    "port": 5060,
    "transport": "UDP"
  },
  "timestamp": 1701944200000
}
```

### Handling VoIP Push in iOS

```swift
// In your PushKit delegate
func pushRegistry(_ registry: PKPushRegistry, didReceiveIncomingPushWith payload: PKPushPayload, for type: PKPushType) {
    guard type == .voIP else { return }
    
    let callerId = payload.dictionaryPayload["caller_id"] as? String ?? "Unknown"
    let callerName = payload.dictionaryPayload["caller_name"] as? String
    let callUUID = UUID(uuidString: payload.dictionaryPayload["call_uuid"] as? String ?? "") ?? UUID()
    
    // Report incoming call to CallKit
    let update = CXCallUpdate()
    update.remoteHandle = CXHandle(type: .phoneNumber, value: callerId)
    update.localizedCallerName = callerName
    update.hasVideo = false
    
    callKitProvider.reportNewIncomingCall(with: callUUID, update: update) { error in
        if let error = error {
            print("Failed to report incoming call: \(error)")
        }
    }
}
```

## Android Integration

### Receiving Call Notifications

```kotlin
// Firebase Cloud Messaging service
class VoIPMessagingService : FirebaseMessagingService() {
    
    override fun onMessageReceived(remoteMessage: RemoteMessage) {
        super.onMessageReceived(remoteMessage)
        
        val data = remoteMessage.data
        val callerId = data["caller_id"] ?: return
        val callerName = data["caller_name"]
        val callUuid = data["call_uuid"] ?: return
        
        // Show incoming call notification
        showIncomingCallNotification(callerId, callerName, callUuid)
    }
    
    private fun showIncomingCallNotification(callerId: String, callerName: String?, callUuid: String) {
        val intent = Intent(this, IncomingCallActivity::class.java).apply {
            putExtra("caller_id", callerId)
            putExtra("caller_name", callerName)
            putExtra("call_uuid", callUuid)
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        
        val pendingIntent = PendingIntent.getActivity(this, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT)
        
        val notification = NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("Incoming Call")
            .setContentText("Call from ${callerName ?: callerId}")
            .setSmallIcon(R.drawable.ic_call)
            .setCategory(NotificationCompat.CATEGORY_CALL)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setFullScreenIntent(pendingIntent, true)
            .setAutoCancel(true)
            .build()
        
        NotificationManagerCompat.from(this).notify(CALL_NOTIFICATION_ID, notification)
    }
}
```

## Error Handling

### Common HTTP Status Codes

- `200 OK` - Request successful
- `201 Created` - Resource created successfully
- `400 Bad Request` - Invalid request data
- `401 Unauthorized` - Authentication required or invalid token
- `404 Not Found` - Resource not found
- `409 Conflict` - Resource conflict (e.g., device already paired)
- `429 Too Many Requests` - Rate limit exceeded
- `500 Internal Server Error` - Server error

### Error Response Format

```json
{
  "statusCode": 400,
  "message": "Validation failed",
  "error": "Bad Request",
  "details": [
    {
      "field": "deviceId",
      "message": "deviceId should not be empty"
    }
  ]
}
```

### WebSocket Error Handling

```javascript
socket.on('error', (error) => {
  console.error('WebSocket error:', error);
  
  switch (error.message) {
    case 'Device ID mismatch':
      // Handle authentication error
      break;
    case 'Target device not connected':
      // Handle offline device
      break;
    case 'Invalid signaling message':
      // Handle malformed message
      break;
  }
});
```

## Rate Limiting

The API implements rate limiting to prevent abuse:

- **Default**: 100 requests per minute per IP
- **Authentication endpoints**: 10 requests per minute per IP
- **WebSocket connections**: 5 connections per device

When rate limited, you'll receive a `429 Too Many Requests` response with headers:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 0
X-RateLimit-Reset: **********
```

## Testing

### Health Check

```bash
GET /health

Response:
{
  "status": "ok",
  "timestamp": "2023-12-07T10:30:00Z",
  "uptime": 3600,
  "environment": "development"
}
```

### API Documentation

Access the interactive API documentation at:
- Development: `http://localhost:3000/api/docs`
- Production: Contact your system administrator

## Best Practices

1. **Token Management**:
   - Store tokens securely
   - Implement automatic token refresh
   - Handle token expiration gracefully

2. **WebSocket Connections**:
   - Implement reconnection logic
   - Handle connection timeouts
   - Validate message formats

3. **Error Handling**:
   - Implement retry logic for transient errors
   - Log errors for debugging
   - Provide user-friendly error messages

4. **Performance**:
   - Batch API calls when possible
   - Implement caching for frequently accessed data
   - Monitor network usage

5. **Security**:
   - Validate all input data
   - Use HTTPS in production
   - Implement proper authentication checks
