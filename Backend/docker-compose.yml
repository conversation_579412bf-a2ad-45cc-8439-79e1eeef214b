version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: voip-postgres
    environment:
      POSTGRES_DB: voip_db
      POSTGRES_USER: voip_user
      POSTGRES_PASSWORD: voip_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - voip-network
    restart: unless-stopped

  # Redis (optional, for caching and session storage)
  redis:
    image: redis:7-alpine
    container_name: voip-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - voip-network
    restart: unless-stopped
    command: redis-server --appendonly yes

  # VoIP Backend Application
  voip-backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: voip-backend
    environment:
      NODE_ENV: production
      PORT: 3000
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USERNAME: voip_user
      DB_PASSWORD: voip_password
      DB_DATABASE: voip_db
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: ${JWT_SECRET:-your-super-secret-jwt-key-change-in-production}
      JWT_REFRESH_SECRET: ${JWT_REFRESH_SECRET:-your-refresh-secret-key}
      APN_KEY_PATH: ${APN_KEY_PATH}
      APN_KEY_ID: ${APN_KEY_ID}
      APN_TEAM_ID: ${APN_TEAM_ID}
      IOS_BUNDLE_ID: ${IOS_BUNDLE_ID:-com.voipreceiver.app}
      STUN_SERVERS: ${STUN_SERVERS:-stun:stun.l.google.com:19302}
      TURN_SERVERS: ${TURN_SERVERS}
      TURN_USERNAME: ${TURN_USERNAME}
      TURN_PASSWORD: ${TURN_PASSWORD}
      SIP_SERVER_HOST: ${SIP_SERVER_HOST:-localhost}
      SIP_SERVER_PORT: ${SIP_SERVER_PORT:-5060}
      SIP_DOMAIN: ${SIP_DOMAIN:-localhost}
    ports:
      - "3000:3000"
    volumes:
      - ./logs:/app/logs
      - ./apn-keys:/app/apn-keys:ro
    networks:
      - voip-network
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx Reverse Proxy (optional)
  nginx:
    image: nginx:alpine
    container_name: voip-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
    networks:
      - voip-network
    depends_on:
      - voip-backend
    restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  voip-network:
    driver: bridge
