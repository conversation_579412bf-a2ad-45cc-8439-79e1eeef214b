# Application Configuration
APP_NAME=VoIP Backend
APP_VERSION=1.0.0
NODE_ENV=development
PORT=3000

# CORS Configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:3001

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=voip_user
DB_PASSWORD=voip_password
DB_DATABASE=voip_db

# Redis Configuration (optional)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=your-refresh-secret-key
JWT_REFRESH_EXPIRES_IN=7d

# Rate Limiting
THROTTLE_TTL=60
THROTTLE_LIMIT=100

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/app.log
LOG_MAX_FILES=14d
LOG_MAX_SIZE=20m

# Apple Push Notification Configuration
APN_KEY_PATH=path/to/your/apn-key.p8
APN_KEY_ID=your-apn-key-id
APN_TEAM_ID=your-apple-team-id
IOS_BUNDLE_ID=com.voipreceiver.app

# WebRTC Configuration
STUN_SERVERS=stun:stun.l.google.com:19302,stun:stun1.l.google.com:19302
TURN_SERVERS=turn:your-turn-server.com:3478
TURN_USERNAME=your-turn-username
TURN_PASSWORD=your-turn-password

# SIP Configuration
SIP_SERVER_HOST=localhost
SIP_SERVER_PORT=5060
SIP_DOMAIN=localhost
SIP_TRANSPORT=UDP
