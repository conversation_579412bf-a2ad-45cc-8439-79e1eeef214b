import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { ConfigService } from '@nestjs/config';
import { <PERSON>ron, CronExpression } from '@nestjs/schedule';
import { PushNotification, PushNotificationType, PushNotificationStatus } from '../database/entities/push-notification.entity';
import { Device, DeviceType } from '../database/entities/device.entity';
import { ApnService, VoIPPushPayload } from './apn.service';
import { DevicesService } from '../devices/devices.service';

@Injectable()
export class PushService {
  private readonly logger = new Logger(PushService.name);
  private readonly bundleId: string;

  constructor(
    @InjectRepository(PushNotification)
    private readonly pushNotificationRepository: Repository<PushNotification>,
    private readonly apnService: ApnService,
    private readonly devicesService: DevicesService,
    private readonly configService: ConfigService,
  ) {
    this.bundleId = this.configService.get('IOS_BUNDLE_ID', 'com.voipreceiver.app');
  }

  /**
   * Send VoIP push notification for incoming call
   */
  async sendVoIPCall(
    deviceId: string,
    callerId: string,
    callerName?: string,
    callUuid?: string,
    serverInfo?: any,
  ): Promise<PushNotification> {
    this.logger.log(`Sending VoIP push to device ${deviceId} for call from ${callerId}`);

    // Find the target device
    const device = await this.devicesService.findByDeviceId(deviceId);

    if (device.deviceType !== DeviceType.IOS) {
      throw new Error('VoIP push notifications are only supported for iOS devices');
    }

    if (!device.pushToken) {
      throw new Error('Device does not have a push token registered');
    }

    if (!device.canReceiveCalls()) {
      throw new Error('Device cannot receive calls (offline or inactive)');
    }

    // Create push notification record
    const pushNotification = this.pushNotificationRepository.create(
      PushNotification.createVoIPCall(deviceId, callerId, callerName, callUuid, serverInfo)
    );

    await this.pushNotificationRepository.save(pushNotification);

    // Send the push notification
    await this.sendPushNotification(pushNotification);

    return pushNotification;
  }

  /**
   * Send call ended notification
   */
  async sendCallEnded(
    deviceId: string,
    callerId: string,
    duration: number,
  ): Promise<PushNotification> {
    this.logger.log(`Sending call ended notification to device ${deviceId}`);

    const device = await this.devicesService.findByDeviceId(deviceId);

    if (!device.pushToken) {
      throw new Error('Device does not have a push token registered');
    }

    // Create push notification record
    const pushNotification = this.pushNotificationRepository.create(
      PushNotification.createCallEnded(deviceId, callerId, duration)
    );

    await this.pushNotificationRepository.save(pushNotification);

    // Send the push notification
    await this.sendPushNotification(pushNotification);

    return pushNotification;
  }

  /**
   * Send device paired notification
   */
  async sendDevicePaired(
    deviceId: string,
    pairedDeviceName: string,
  ): Promise<PushNotification> {
    this.logger.log(`Sending device paired notification to device ${deviceId}`);

    const device = await this.devicesService.findByDeviceId(deviceId);

    if (!device.pushToken) {
      throw new Error('Device does not have a push token registered');
    }

    // Create push notification record
    const pushNotification = this.pushNotificationRepository.create(
      PushNotification.createDevicePaired(deviceId, pairedDeviceName)
    );

    await this.pushNotificationRepository.save(pushNotification);

    // Send the push notification
    await this.sendPushNotification(pushNotification);

    return pushNotification;
  }

  /**
   * Send custom push notification
   */
  async sendCustomPush(
    deviceId: string,
    type: PushNotificationType,
    title: string,
    body: string,
    payload: any,
    priority: number = 5,
  ): Promise<PushNotification> {
    this.logger.log(`Sending custom push notification to device ${deviceId}`);

    const device = await this.devicesService.findByDeviceId(deviceId);

    if (!device.pushToken) {
      throw new Error('Device does not have a push token registered');
    }

    // Create push notification record
    const pushNotification = this.pushNotificationRepository.create({
      deviceId,
      type,
      title,
      body,
      payload,
      priority,
    });

    await this.pushNotificationRepository.save(pushNotification);

    // Send the push notification
    await this.sendPushNotification(pushNotification);

    return pushNotification;
  }

  /**
   * Actually send the push notification via APN
   */
  private async sendPushNotification(pushNotification: PushNotification): Promise<void> {
    const startTime = Date.now();

    try {
      const device = await this.devicesService.findByDeviceId(pushNotification.deviceId);

      if (!device.pushToken) {
        throw new Error('Device push token not available');
      }

      let result;

      if (pushNotification.type === PushNotificationType.VOIP_CALL) {
        // Send VoIP push notification
        const voipPayload: VoIPPushPayload = {
          caller_id: pushNotification.payload.caller_id,
          caller_name: pushNotification.payload.caller_name,
          call_uuid: pushNotification.payload.call_uuid,
          server_info: pushNotification.payload.server_info,
        };

        result = await this.apnService.sendVoIPPush(
          device.pushToken,
          voipPayload,
          this.bundleId,
        );
      } else {
        // Send regular push notification
        result = await this.apnService.sendPush(
          device.pushToken,
          pushNotification.title,
          pushNotification.body,
          pushNotification.payload,
          this.bundleId,
          pushNotification.priority,
        );
      }

      // Update push notification record
      pushNotification.setResponseTime(startTime);

      if (result.success) {
        pushNotification.markAsSent(result.apnsId);
        this.logger.log(`Push notification sent successfully: ${pushNotification.id}`);
      } else {
        pushNotification.markAsFailed({
          message: result.error,
          apnsError: result.error,
        });
        this.logger.error(`Push notification failed: ${pushNotification.id} - ${result.error}`);
      }

      await this.pushNotificationRepository.save(pushNotification);

    } catch (error) {
      pushNotification.setResponseTime(startTime);
      pushNotification.markAsFailed({
        message: error.message,
        details: error.stack,
      });

      await this.pushNotificationRepository.save(pushNotification);

      this.logger.error(`Error sending push notification ${pushNotification.id}:`, error);
      throw error;
    }
  }

  /**
   * Retry failed push notifications
   */
  async retryFailedNotifications(): Promise<void> {
    const failedNotifications = await this.pushNotificationRepository.find({
      where: {
        status: PushNotificationStatus.FAILED,
      },
      take: 100, // Limit to prevent overwhelming the system
    });

    this.logger.log(`Found ${failedNotifications.length} failed notifications to retry`);

    for (const notification of failedNotifications) {
      if (notification.canRetry) {
        try {
          await this.sendPushNotification(notification);
        } catch (error) {
          this.logger.error(`Failed to retry notification ${notification.id}:`, error);
        }
      } else if (notification.isExpired) {
        notification.markAsExpired();
        await this.pushNotificationRepository.save(notification);
      }
    }
  }

  /**
   * Get push notification statistics
   */
  async getPushStats(): Promise<{
    total: number;
    sent: number;
    delivered: number;
    failed: number;
    pending: number;
    expired: number;
  }> {
    const [total, sent, delivered, failed, pending, expired] = await Promise.all([
      this.pushNotificationRepository.count(),
      this.pushNotificationRepository.count({ where: { status: PushNotificationStatus.SENT } }),
      this.pushNotificationRepository.count({ where: { status: PushNotificationStatus.DELIVERED } }),
      this.pushNotificationRepository.count({ where: { status: PushNotificationStatus.FAILED } }),
      this.pushNotificationRepository.count({ where: { status: PushNotificationStatus.PENDING } }),
      this.pushNotificationRepository.count({ where: { status: PushNotificationStatus.EXPIRED } }),
    ]);

    return {
      total,
      sent,
      delivered,
      failed,
      pending,
      expired,
    };
  }

  /**
   * Get push notifications for a device
   */
  async getDevicePushNotifications(
    deviceId: string,
    limit: number = 50,
  ): Promise<PushNotification[]> {
    return await this.pushNotificationRepository.find({
      where: { deviceId },
      order: { createdAt: 'DESC' },
      take: limit,
    });
  }

  /**
   * Clean up old push notifications (scheduled task)
   */
  @Cron(CronExpression.EVERY_DAY_AT_2AM)
  async cleanupOldNotifications(): Promise<void> {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

    const result = await this.pushNotificationRepository
      .createQueryBuilder()
      .delete()
      .where('created_at < :thirtyDaysAgo', { thirtyDaysAgo })
      .execute();

    this.logger.log(`Cleaned up ${result.affected} old push notifications`);
  }

  /**
   * Retry failed notifications (scheduled task)
   */
  @Cron(CronExpression.EVERY_5_MINUTES)
  async scheduledRetryFailedNotifications(): Promise<void> {
    await this.retryFailedNotifications();
  }
}
