import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as apn from 'node-apn';

export interface VoIPPushPayload {
  caller_id: string;
  caller_name?: string;
  call_uuid: string;
  server_info?: {
    sip_server: string;
    port: number;
    transport: string;
  };
}

export interface PushResult {
  success: boolean;
  apnsId?: string;
  error?: string;
  responseTime: number;
}

@Injectable()
export class ApnService implements OnModuleInit {
  private readonly logger = new Logger(ApnService.name);
  private apnProvider: apn.Provider;
  private isInitialized = false;

  constructor(private readonly configService: ConfigService) {}

  async onModuleInit() {
    await this.initializeApnProvider();
  }

  private async initializeApnProvider() {
    try {
      const environment = this.configService.get('NODE_ENV');
      const keyPath = this.configService.get('APN_KEY_PATH');
      const keyId = this.configService.get('APN_KEY_ID');
      const teamId = this.configService.get('APN_TEAM_ID');

      if (!keyPath || !keyId || !teamId) {
        this.logger.warn('APN credentials not configured. VoIP push notifications will not work.');
        return;
      }

      const options: apn.ProviderOptions = {
        token: {
          key: keyPath,
          keyId: keyId,
          teamId: teamId,
        },
        production: environment === 'production',
      };

      this.apnProvider = new apn.Provider(options);
      this.isInitialized = true;

      this.logger.log(`APN Provider initialized for ${environment} environment`);

      // Handle provider events
      this.apnProvider.on('connected', () => {
        this.logger.log('Connected to Apple Push Notification service');
      });

      this.apnProvider.on('disconnected', () => {
        this.logger.warn('Disconnected from Apple Push Notification service');
      });

      this.apnProvider.on('socketError', (error) => {
        this.logger.error('APN Socket error:', error);
      });

      this.apnProvider.on('transmissionError', (errorCode, notification, device) => {
        this.logger.error(`APN Transmission error ${errorCode} for device ${device}:`, notification);
      });

    } catch (error) {
      this.logger.error('Failed to initialize APN Provider:', error);
    }
  }

  /**
   * Send VoIP push notification
   */
  async sendVoIPPush(
    deviceToken: string,
    payload: VoIPPushPayload,
    bundleId: string,
  ): Promise<PushResult> {
    if (!this.isInitialized) {
      return {
        success: false,
        error: 'APN Provider not initialized',
        responseTime: 0,
      };
    }

    const startTime = Date.now();

    try {
      const notification = new apn.Notification();
      
      // VoIP push notification configuration
      notification.topic = `${bundleId}.voip`;
      notification.pushType = 'voip';
      notification.priority = 10;
      notification.expiry = Math.floor(Date.now() / 1000) + 30; // 30 seconds

      // Alert configuration
      notification.alert = {
        title: 'Incoming Call',
        body: `Incoming call from ${payload.caller_name || payload.caller_id}`,
      };

      // Custom payload
      notification.payload = {
        caller_id: payload.caller_id,
        caller_name: payload.caller_name,
        call_uuid: payload.call_uuid,
        server_info: payload.server_info,
        timestamp: Date.now(),
      };

      // Send notification
      const result = await this.apnProvider.send(notification, deviceToken);
      const responseTime = Date.now() - startTime;

      if (result.sent.length > 0) {
        this.logger.log(`VoIP push sent successfully to ${deviceToken}`);
        return {
          success: true,
          apnsId: result.sent[0].device,
          responseTime,
        };
      } else if (result.failed.length > 0) {
        const failure = result.failed[0];
        this.logger.error(`VoIP push failed for ${deviceToken}:`, failure.error);
        return {
          success: false,
          error: failure.error || 'Unknown error',
          responseTime,
        };
      } else {
        return {
          success: false,
          error: 'No response from APN',
          responseTime,
        };
      }

    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.logger.error(`Error sending VoIP push to ${deviceToken}:`, error);
      return {
        success: false,
        error: error.message || 'Unknown error',
        responseTime,
      };
    }
  }

  /**
   * Send regular push notification
   */
  async sendPush(
    deviceToken: string,
    title: string,
    body: string,
    payload: any,
    bundleId: string,
    priority: number = 5,
  ): Promise<PushResult> {
    if (!this.isInitialized) {
      return {
        success: false,
        error: 'APN Provider not initialized',
        responseTime: 0,
      };
    }

    const startTime = Date.now();

    try {
      const notification = new apn.Notification();
      
      // Regular push notification configuration
      notification.topic = bundleId;
      notification.priority = priority;
      notification.expiry = Math.floor(Date.now() / 1000) + 3600; // 1 hour

      // Alert configuration
      notification.alert = {
        title,
        body,
      };

      // Sound
      notification.sound = 'default';

      // Custom payload
      notification.payload = payload;

      // Send notification
      const result = await this.apnProvider.send(notification, deviceToken);
      const responseTime = Date.now() - startTime;

      if (result.sent.length > 0) {
        this.logger.log(`Push notification sent successfully to ${deviceToken}`);
        return {
          success: true,
          apnsId: result.sent[0].device,
          responseTime,
        };
      } else if (result.failed.length > 0) {
        const failure = result.failed[0];
        this.logger.error(`Push notification failed for ${deviceToken}:`, failure.error);
        return {
          success: false,
          error: failure.error || 'Unknown error',
          responseTime,
        };
      } else {
        return {
          success: false,
          error: 'No response from APN',
          responseTime,
        };
      }

    } catch (error) {
      const responseTime = Date.now() - startTime;
      this.logger.error(`Error sending push notification to ${deviceToken}:`, error);
      return {
        success: false,
        error: error.message || 'Unknown error',
        responseTime,
      };
    }
  }

  /**
   * Validate device token format
   */
  isValidDeviceToken(token: string): boolean {
    // iOS device tokens are 64 characters long and contain only hex characters
    const tokenRegex = /^[0-9a-fA-F]{64}$/;
    return tokenRegex.test(token);
  }

  /**
   * Check if APN service is available
   */
  isAvailable(): boolean {
    return this.isInitialized;
  }

  /**
   * Shutdown APN provider
   */
  async shutdown(): Promise<void> {
    if (this.apnProvider) {
      await this.apnProvider.shutdown();
      this.logger.log('APN Provider shutdown');
    }
  }
}
