import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Query,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { PushService } from './push.service';
import { PushNotification, PushNotificationType } from '../database/entities/push-notification.entity';

class SendVoIPPushDto {
  deviceId: string;
  callerId: string;
  callerName?: string;
  callUuid?: string;
  serverInfo?: any;
}

class SendCustomPushDto {
  deviceId: string;
  type: PushNotificationType;
  title: string;
  body: string;
  payload: any;
  priority?: number;
}

@ApiTags('Push Notifications')
@ApiBearerAuth()
@Controller('push')
export class PushController {
  constructor(private readonly pushService: PushService) {}

  @Post('voip-call')
  @ApiOperation({ summary: 'Send VoIP push notification for incoming call' })
  @ApiResponse({ status: 201, description: 'VoIP push sent successfully', type: PushNotification })
  @ApiResponse({ status: 400, description: 'Invalid push request' })
  async sendVoIPCall(@Body() body: SendVoIPPushDto): Promise<PushNotification> {
    return await this.pushService.sendVoIPCall(
      body.deviceId,
      body.callerId,
      body.callerName,
      body.callUuid,
      body.serverInfo,
    );
  }

  @Post('call-ended')
  @ApiOperation({ summary: 'Send call ended notification' })
  @ApiResponse({ status: 201, description: 'Call ended notification sent successfully', type: PushNotification })
  async sendCallEnded(
    @Body() body: { deviceId: string; callerId: string; duration: number },
  ): Promise<PushNotification> {
    return await this.pushService.sendCallEnded(
      body.deviceId,
      body.callerId,
      body.duration,
    );
  }

  @Post('device-paired')
  @ApiOperation({ summary: 'Send device paired notification' })
  @ApiResponse({ status: 201, description: 'Device paired notification sent successfully', type: PushNotification })
  async sendDevicePaired(
    @Body() body: { deviceId: string; pairedDeviceName: string },
  ): Promise<PushNotification> {
    return await this.pushService.sendDevicePaired(
      body.deviceId,
      body.pairedDeviceName,
    );
  }

  @Post('custom')
  @ApiOperation({ summary: 'Send custom push notification' })
  @ApiResponse({ status: 201, description: 'Custom push sent successfully', type: PushNotification })
  async sendCustomPush(@Body() body: SendCustomPushDto): Promise<PushNotification> {
    return await this.pushService.sendCustomPush(
      body.deviceId,
      body.type,
      body.title,
      body.body,
      body.payload,
      body.priority,
    );
  }

  @Post('retry-failed')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Retry failed push notifications' })
  @ApiResponse({ status: 204, description: 'Failed notifications retry initiated' })
  async retryFailedNotifications(): Promise<void> {
    await this.pushService.retryFailedNotifications();
  }

  @Get('stats')
  @ApiOperation({ summary: 'Get push notification statistics' })
  @ApiResponse({ status: 200, description: 'Push statistics retrieved successfully' })
  async getPushStats() {
    return await this.pushService.getPushStats();
  }

  @Get('device/:deviceId')
  @ApiOperation({ summary: 'Get push notifications for a device' })
  @ApiQuery({ name: 'limit', type: Number, required: false, description: 'Number of notifications to return (default: 50)' })
  @ApiResponse({ status: 200, description: 'Device push notifications retrieved successfully', type: [PushNotification] })
  async getDevicePushNotifications(
    @Param('deviceId') deviceId: string,
    @Query('limit') limit?: number,
  ): Promise<PushNotification[]> {
    return await this.pushService.getDevicePushNotifications(deviceId, limit);
  }
}
