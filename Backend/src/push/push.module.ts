import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ConfigModule } from '@nestjs/config';
import { PushService } from './push.service';
import { PushController } from './push.controller';
import { ApnService } from './apn.service';
import { PushNotification } from '../database/entities/push-notification.entity';
import { Device } from '../database/entities/device.entity';
import { DevicesModule } from '../devices/devices.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([PushNotification, Device]),
    ConfigModule,
    DevicesModule,
  ],
  controllers: [PushController],
  providers: [PushService, ApnService],
  exports: [PushService, ApnService],
})
export class PushModule {}
