import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  ConnectedSocket,
  MessageBody,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Logger, UseGuards } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { WebrtcService, SignalingMessage } from './webrtc.service';
import { SipBridgeService } from './sip-bridge.service';
import { DevicesService } from '../devices/devices.service';
import { CallsService } from '../calls/calls.service';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

interface AuthenticatedSocket extends Socket {
  deviceId?: string;
  userId?: string;
}

@WebSocketGateway({
  cors: {
    origin: '*',
    methods: ['GET', 'POST'],
  },
  namespace: '/webrtc',
})
@UseGuards(JwtAuthGuard)
export class WebrtcGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(WebrtcGateway.name);
  private readonly connectedDevices = new Map<string, AuthenticatedSocket>();

  constructor(
    private readonly webrtcService: WebrtcService,
    private readonly sipBridgeService: SipBridgeService,
    private readonly devicesService: DevicesService,
    private readonly callsService: CallsService,
  ) {}

  async handleConnection(client: AuthenticatedSocket) {
    try {
      // Extract device ID from authentication (this would be set by the JWT guard)
      const deviceId = client.handshake.auth?.deviceId || client.handshake.query?.deviceId;
      
      if (!deviceId) {
        this.logger.warn(`Client ${client.id} connected without device ID`);
        client.disconnect();
        return;
      }

      // Verify device exists
      const device = await this.devicesService.findByDeviceId(deviceId as string);
      
      client.deviceId = deviceId as string;
      this.connectedDevices.set(deviceId as string, client);

      // Update device last activity
      await this.devicesService.updateLastActivity(deviceId as string);

      this.logger.log(`Device ${deviceId} connected via WebSocket (${client.id})`);

      // Send WebRTC configuration
      client.emit('webrtc-config', this.webrtcService.getWebRTCConfig());

      // Send SIP configuration
      client.emit('sip-config', this.sipBridgeService.getSipConfig());

    } catch (error) {
      this.logger.error(`Error handling connection for client ${client.id}:`, error);
      client.disconnect();
    }
  }

  handleDisconnect(client: AuthenticatedSocket) {
    if (client.deviceId) {
      this.connectedDevices.delete(client.deviceId);
      this.logger.log(`Device ${client.deviceId} disconnected (${client.id})`);
    }
  }

  /**
   * Handle WebRTC signaling messages
   */
  @SubscribeMessage('webrtc-signal')
  async handleWebRTCSignal(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() message: SignalingMessage,
  ) {
    try {
      if (!this.webrtcService.validateSignalingMessage(message)) {
        client.emit('error', { message: 'Invalid signaling message' });
        return;
      }

      // Ensure the message is from the authenticated device
      if (message.fromDevice !== client.deviceId) {
        client.emit('error', { message: 'Device ID mismatch' });
        return;
      }

      this.logger.log(`WebRTC signal from ${message.fromDevice} to ${message.toDevice}: ${message.type}`);

      // Process different message types
      let result;
      switch (message.type) {
        case 'offer':
          result = this.webrtcService.processOffer(message);
          break;
        case 'answer':
          result = this.webrtcService.processAnswer(message);
          break;
        case 'ice-candidate':
          result = this.webrtcService.processIceCandidate(message);
          break;
      }

      if (result && !result.success) {
        client.emit('error', { message: result.error });
        return;
      }

      // Forward message to target device
      const targetSocket = this.connectedDevices.get(message.toDevice);
      if (targetSocket) {
        targetSocket.emit('webrtc-signal', message);
      } else {
        client.emit('error', { message: 'Target device not connected' });
      }

    } catch (error) {
      this.logger.error(`Error handling WebRTC signal:`, error);
      client.emit('error', { message: 'Internal server error' });
    }
  }

  /**
   * Handle call initiation
   */
  @SubscribeMessage('initiate-call')
  async handleInitiateCall(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { callerId: string; callerName?: string },
  ) {
    try {
      if (!client.deviceId) {
        client.emit('error', { message: 'Device not authenticated' });
        return;
      }

      const callLog = await this.callsService.initiateCall({
        androidDeviceId: client.deviceId,
        callerId: data.callerId,
        callerName: data.callerName,
      });

      // Create WebRTC connection session
      const device = await this.devicesService.findByDeviceId(client.deviceId);
      const pairedDevice = await this.devicesService.findById(device.pairedDeviceId);

      this.webrtcService.createConnection(callLog.callId, [
        device.deviceId,
        pairedDevice.deviceId,
      ]);

      // Create SIP session
      await this.sipBridgeService.createSession(
        callLog.callId,
        device.deviceId,
        pairedDevice.deviceId,
      );

      client.emit('call-initiated', {
        callId: callLog.callId,
        status: callLog.status,
      });

      this.logger.log(`Call initiated: ${callLog.callId}`);

    } catch (error) {
      this.logger.error(`Error initiating call:`, error);
      client.emit('error', { message: error.message });
    }
  }

  /**
   * Handle call answer
   */
  @SubscribeMessage('answer-call')
  async handleAnswerCall(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { callId: string },
  ) {
    try {
      const callLog = await this.callsService.answerCall(data.callId);

      // Notify all participants
      this.notifyCallParticipants(data.callId, 'call-answered', {
        callId: data.callId,
        status: callLog.status,
      });

      this.logger.log(`Call answered: ${data.callId}`);

    } catch (error) {
      this.logger.error(`Error answering call:`, error);
      client.emit('error', { message: error.message });
    }
  }

  /**
   * Handle call end
   */
  @SubscribeMessage('end-call')
  async handleEndCall(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { callId: string; reason?: string },
  ) {
    try {
      const callLog = await this.callsService.endCall(data.callId, data.reason as any);

      // Clean up WebRTC connection
      this.webrtcService.removeConnection(data.callId);

      // End SIP session
      await this.sipBridgeService.sendBye(data.callId);

      // Notify all participants
      this.notifyCallParticipants(data.callId, 'call-ended', {
        callId: data.callId,
        status: callLog.status,
        duration: callLog.duration,
      });

      this.logger.log(`Call ended: ${data.callId}`);

    } catch (error) {
      this.logger.error(`Error ending call:`, error);
      client.emit('error', { message: error.message });
    }
  }

  /**
   * Handle call quality updates
   */
  @SubscribeMessage('call-quality')
  async handleCallQuality(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: {
      callId: string;
      qualityMetrics?: any;
      networkStats?: any;
    },
  ) {
    try {
      await this.callsService.updateCall(data.callId, {
        qualityMetrics: data.qualityMetrics,
        networkStats: data.networkStats,
      });

      // Forward quality info to other participants
      this.notifyCallParticipants(data.callId, 'call-quality-update', data, client.deviceId);

    } catch (error) {
      this.logger.error(`Error updating call quality:`, error);
    }
  }

  /**
   * Get WebRTC configuration
   */
  @SubscribeMessage('get-webrtc-config')
  handleGetWebRTCConfig(@ConnectedSocket() client: AuthenticatedSocket) {
    client.emit('webrtc-config', this.webrtcService.getWebRTCConfig());
  }

  /**
   * Get SIP configuration
   */
  @SubscribeMessage('get-sip-config')
  handleGetSipConfig(@ConnectedSocket() client: AuthenticatedSocket) {
    client.emit('sip-config', this.sipBridgeService.getSipConfig());
  }

  /**
   * Notify all participants in a call
   */
  private async notifyCallParticipants(
    callId: string,
    event: string,
    data: any,
    excludeDeviceId?: string,
  ) {
    try {
      const callLog = await this.callsService.findCallById(callId);
      const deviceIds = [
        callLog.initiatorDevice.deviceId,
        callLog.receiverDevice.deviceId,
      ].filter(id => id !== excludeDeviceId);

      deviceIds.forEach(deviceId => {
        const socket = this.connectedDevices.get(deviceId);
        if (socket) {
          socket.emit(event, data);
        }
      });

    } catch (error) {
      this.logger.error(`Error notifying call participants:`, error);
    }
  }

  /**
   * Broadcast message to all connected devices
   */
  broadcastToAll(event: string, data: any) {
    this.server.emit(event, data);
  }

  /**
   * Send message to specific device
   */
  sendToDevice(deviceId: string, event: string, data: any) {
    const socket = this.connectedDevices.get(deviceId);
    if (socket) {
      socket.emit(event, data);
    }
  }

  /**
   * Get connected devices count
   */
  getConnectedDevicesCount(): number {
    return this.connectedDevices.size;
  }

  /**
   * Get connected device IDs
   */
  getConnectedDeviceIds(): string[] {
    return Array.from(this.connectedDevices.keys());
  }
}
