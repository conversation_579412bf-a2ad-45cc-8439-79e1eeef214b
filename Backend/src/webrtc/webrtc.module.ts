import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { WebrtcGateway } from './webrtc.gateway';
import { WebrtcService } from './webrtc.service';
import { SipBridgeService } from './sip-bridge.service';
import { DevicesModule } from '../devices/devices.module';
import { CallsModule } from '../calls/calls.module';

@Module({
  imports: [
    ConfigModule,
    DevicesModule,
    CallsModule,
  ],
  providers: [WebrtcGateway, WebrtcService, SipBridgeService],
  exports: [WebrtcService, SipBridgeService],
})
export class WebrtcModule {}
