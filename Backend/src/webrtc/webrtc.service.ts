import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

export interface WebRTCConfig {
  iceServers: RTCIceServer[];
}

export interface SignalingMessage {
  type: 'offer' | 'answer' | 'ice-candidate' | 'call-request' | 'call-response' | 'call-end';
  callId: string;
  fromDevice: string;
  toDevice: string;
  data?: any;
}

@Injectable()
export class WebrtcService {
  private readonly logger = new Logger(WebrtcService.name);
  private readonly activeConnections = new Map<string, {
    callId: string;
    devices: string[];
    startTime: Date;
  }>();

  constructor(private readonly configService: ConfigService) {}

  /**
   * Get WebRTC configuration for clients
   */
  getWebRTCConfig(): WebRTCConfig {
    const stunServers = this.configService.get('STUN_SERVERS', 'stun:stun.l.google.com:19302').split(',');
    const turnServers = this.configService.get('TURN_SERVERS', '').split(',').filter(Boolean);
    const turnUsername = this.configService.get('TURN_USERNAME');
    const turnPassword = this.configService.get('TURN_PASSWORD');

    const iceServers: RTCIceServer[] = [
      // STUN servers
      ...stunServers.map(url => ({ urls: url.trim() })),
    ];

    // Add TURN servers if configured
    if (turnServers.length > 0 && turnUsername && turnPassword) {
      iceServers.push(
        ...turnServers.map(url => ({
          urls: url.trim(),
          username: turnUsername,
          credential: turnPassword,
        }))
      );
    }

    return { iceServers };
  }

  /**
   * Create a WebRTC connection session
   */
  createConnection(callId: string, devices: string[]): void {
    this.activeConnections.set(callId, {
      callId,
      devices,
      startTime: new Date(),
    });

    this.logger.log(`Created WebRTC connection for call ${callId} with devices: ${devices.join(', ')}`);
  }

  /**
   * Remove WebRTC connection session
   */
  removeConnection(callId: string): void {
    if (this.activeConnections.has(callId)) {
      this.activeConnections.delete(callId);
      this.logger.log(`Removed WebRTC connection for call ${callId}`);
    }
  }

  /**
   * Get active WebRTC connections
   */
  getActiveConnections(): Array<{
    callId: string;
    devices: string[];
    startTime: Date;
  }> {
    return Array.from(this.activeConnections.values());
  }

  /**
   * Validate signaling message
   */
  validateSignalingMessage(message: SignalingMessage): boolean {
    if (!message.type || !message.callId || !message.fromDevice || !message.toDevice) {
      return false;
    }

    const validTypes = ['offer', 'answer', 'ice-candidate', 'call-request', 'call-response', 'call-end'];
    return validTypes.includes(message.type);
  }

  /**
   * Process WebRTC offer
   */
  processOffer(message: SignalingMessage): {
    success: boolean;
    error?: string;
  } {
    try {
      if (message.type !== 'offer') {
        return { success: false, error: 'Invalid message type for offer' };
      }

      if (!message.data || !message.data.sdp) {
        return { success: false, error: 'Missing SDP in offer' };
      }

      this.logger.log(`Processing WebRTC offer for call ${message.callId}`);

      // Validate SDP format (basic validation)
      const sdp = message.data.sdp;
      if (!sdp.includes('v=0') || !sdp.includes('m=audio')) {
        return { success: false, error: 'Invalid SDP format' };
      }

      return { success: true };

    } catch (error) {
      this.logger.error(`Error processing WebRTC offer:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Process WebRTC answer
   */
  processAnswer(message: SignalingMessage): {
    success: boolean;
    error?: string;
  } {
    try {
      if (message.type !== 'answer') {
        return { success: false, error: 'Invalid message type for answer' };
      }

      if (!message.data || !message.data.sdp) {
        return { success: false, error: 'Missing SDP in answer' };
      }

      this.logger.log(`Processing WebRTC answer for call ${message.callId}`);

      // Validate SDP format (basic validation)
      const sdp = message.data.sdp;
      if (!sdp.includes('v=0') || !sdp.includes('m=audio')) {
        return { success: false, error: 'Invalid SDP format' };
      }

      return { success: true };

    } catch (error) {
      this.logger.error(`Error processing WebRTC answer:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Process ICE candidate
   */
  processIceCandidate(message: SignalingMessage): {
    success: boolean;
    error?: string;
  } {
    try {
      if (message.type !== 'ice-candidate') {
        return { success: false, error: 'Invalid message type for ICE candidate' };
      }

      if (!message.data || (!message.data.candidate && message.data.candidate !== null)) {
        return { success: false, error: 'Missing candidate in ICE candidate message' };
      }

      this.logger.log(`Processing ICE candidate for call ${message.callId}`);

      // Validate ICE candidate format (basic validation)
      if (message.data.candidate && typeof message.data.candidate !== 'string') {
        return { success: false, error: 'Invalid ICE candidate format' };
      }

      return { success: true };

    } catch (error) {
      this.logger.error(`Error processing ICE candidate:`, error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Generate WebRTC statistics report
   */
  generateStatsReport(): {
    activeConnections: number;
    totalConnectionsToday: number;
    averageConnectionDuration: number;
    configuredStunServers: number;
    configuredTurnServers: number;
  } {
    const config = this.getWebRTCConfig();
    const stunServers = config.iceServers.filter(server => 
      server.urls.toString().startsWith('stun:')
    ).length;
    const turnServers = config.iceServers.filter(server => 
      server.urls.toString().startsWith('turn:')
    ).length;

    // Calculate average connection duration for active connections
    const now = new Date();
    const activeConnections = this.getActiveConnections();
    const totalDuration = activeConnections.reduce((sum, conn) => {
      return sum + (now.getTime() - conn.startTime.getTime());
    }, 0);
    const averageConnectionDuration = activeConnections.length > 0 
      ? totalDuration / activeConnections.length / 1000 // Convert to seconds
      : 0;

    return {
      activeConnections: activeConnections.length,
      totalConnectionsToday: activeConnections.length, // This would need to be tracked separately
      averageConnectionDuration,
      configuredStunServers: stunServers,
      configuredTurnServers: turnServers,
    };
  }

  /**
   * Check WebRTC service health
   */
  checkHealth(): {
    status: 'healthy' | 'degraded' | 'unhealthy';
    details: {
      stunServersConfigured: boolean;
      turnServersConfigured: boolean;
      activeConnections: number;
    };
  } {
    const config = this.getWebRTCConfig();
    const stunServersConfigured = config.iceServers.some(server => 
      server.urls.toString().startsWith('stun:')
    );
    const turnServersConfigured = config.iceServers.some(server => 
      server.urls.toString().startsWith('turn:')
    );
    const activeConnections = this.activeConnections.size;

    let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';

    if (!stunServersConfigured) {
      status = 'degraded';
    }

    if (!stunServersConfigured && !turnServersConfigured) {
      status = 'unhealthy';
    }

    return {
      status,
      details: {
        stunServersConfigured,
        turnServersConfigured,
        activeConnections,
      },
    };
  }
}
