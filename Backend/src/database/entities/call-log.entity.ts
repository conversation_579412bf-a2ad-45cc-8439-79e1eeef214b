import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Device } from './device.entity';

export enum CallDirection {
  INBOUND = 'inbound',
  OUTBOUND = 'outbound',
}

export enum CallStatus {
  INITIATED = 'initiated',
  RINGING = 'ringing',
  ANSWERED = 'answered',
  CONNECTED = 'connected',
  ON_HOLD = 'on_hold',
  ENDED = 'ended',
  FAILED = 'failed',
  MISSED = 'missed',
  CANCELLED = 'cancelled',
}

export enum CallEndReason {
  NORMAL = 'normal',
  BUSY = 'busy',
  NO_ANSWER = 'no_answer',
  DECLINED = 'declined',
  NETWORK_ERROR = 'network_error',
  SERVER_ERROR = 'server_error',
  CANCELLED = 'cancelled',
}

@Entity('call_logs')
@Index(['callId'], { unique: true })
@Index(['initiatorDeviceId'])
@Index(['receiverDeviceId'])
@Index(['startTime'])
@Index(['status'])
export class CallLog {
  @ApiProperty({ description: 'Unique call log identifier' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: 'Unique call identifier' })
  @Column({ name: 'call_id', unique: true })
  callId: string;

  @ApiProperty({ description: 'Initiator device ID' })
  @Column({ name: 'initiator_device_id' })
  initiatorDeviceId: string;

  @ApiProperty({ description: 'Receiver device ID' })
  @Column({ name: 'receiver_device_id' })
  receiverDeviceId: string;

  @ApiProperty({ description: 'Caller phone number or ID' })
  @Column({ name: 'caller_id' })
  callerId: string;

  @ApiProperty({ description: 'Caller display name' })
  @Column({ name: 'caller_name', nullable: true })
  callerName?: string;

  @ApiProperty({ enum: CallDirection, description: 'Call direction' })
  @Column({
    type: 'enum',
    enum: CallDirection,
  })
  direction: CallDirection;

  @ApiProperty({ enum: CallStatus, description: 'Current call status' })
  @Column({
    type: 'enum',
    enum: CallStatus,
    default: CallStatus.INITIATED,
  })
  status: CallStatus;

  @ApiProperty({ description: 'Call start timestamp' })
  @Column({ name: 'start_time', type: 'timestamp' })
  startTime: Date;

  @ApiProperty({ description: 'Call answer timestamp' })
  @Column({ name: 'answer_time', type: 'timestamp', nullable: true })
  answerTime?: Date;

  @ApiProperty({ description: 'Call end timestamp' })
  @Column({ name: 'end_time', type: 'timestamp', nullable: true })
  endTime?: Date;

  @ApiProperty({ description: 'Call duration in seconds' })
  @Column({ type: 'integer', default: 0 })
  duration: number;

  @ApiProperty({ enum: CallEndReason, description: 'Reason for call ending' })
  @Column({
    type: 'enum',
    enum: CallEndReason,
    nullable: true,
    name: 'end_reason',
  })
  endReason?: CallEndReason;

  @ApiProperty({ description: 'Audio codec used' })
  @Column({ nullable: true })
  codec?: string;

  @ApiProperty({ description: 'Call quality metrics' })
  @Column({ type: 'jsonb', nullable: true, name: 'quality_metrics' })
  qualityMetrics?: {
    jitter?: number;
    packetLoss?: number;
    roundTripTime?: number;
    audioLevel?: number;
    qualityScore?: 'good' | 'fair' | 'poor';
  };

  @ApiProperty({ description: 'Network statistics' })
  @Column({ type: 'jsonb', nullable: true, name: 'network_stats' })
  networkStats?: {
    bytesReceived?: number;
    bytesSent?: number;
    packetsReceived?: number;
    packetsSent?: number;
    packetsLost?: number;
  };

  @ApiProperty({ description: 'SIP call details' })
  @Column({ type: 'jsonb', nullable: true, name: 'sip_details' })
  sipDetails?: {
    callId?: string;
    fromTag?: string;
    toTag?: string;
    branch?: string;
    userAgent?: string;
  };

  @ApiProperty({ description: 'Error information if call failed' })
  @Column({ type: 'jsonb', nullable: true, name: 'error_info' })
  errorInfo?: {
    code?: number;
    message?: string;
    details?: string;
  };

  @ApiProperty({ description: 'Call log creation timestamp' })
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relations
  @ApiProperty({ type: () => Device, description: 'Initiator device' })
  @ManyToOne(() => Device, (device) => device.initiatedCalls)
  @JoinColumn({ name: 'initiator_device_id' })
  initiatorDevice: Device;

  @ApiProperty({ type: () => Device, description: 'Receiver device' })
  @ManyToOne(() => Device, (device) => device.receivedCalls)
  @JoinColumn({ name: 'receiver_device_id' })
  receiverDevice: Device;

  // Virtual properties
  get wasAnswered(): boolean {
    return this.status === CallStatus.ANSWERED || 
           this.status === CallStatus.CONNECTED || 
           this.status === CallStatus.ENDED;
  }

  get isActive(): boolean {
    return [
      CallStatus.INITIATED,
      CallStatus.RINGING,
      CallStatus.ANSWERED,
      CallStatus.CONNECTED,
      CallStatus.ON_HOLD,
    ].includes(this.status);
  }

  // Methods
  updateDuration(): void {
    if (this.answerTime && this.endTime) {
      this.duration = Math.floor(
        (this.endTime.getTime() - this.answerTime.getTime()) / 1000
      );
    }
  }

  setAnswered(): void {
    this.status = CallStatus.ANSWERED;
    this.answerTime = new Date();
  }

  setConnected(): void {
    this.status = CallStatus.CONNECTED;
    if (!this.answerTime) {
      this.answerTime = new Date();
    }
  }

  setEnded(reason: CallEndReason = CallEndReason.NORMAL): void {
    this.status = CallStatus.ENDED;
    this.endTime = new Date();
    this.endReason = reason;
    this.updateDuration();
  }

  setFailed(reason: CallEndReason, errorInfo?: any): void {
    this.status = CallStatus.FAILED;
    this.endTime = new Date();
    this.endReason = reason;
    this.errorInfo = errorInfo;
    this.updateDuration();
  }
}
