import {
  Entity,
  PrimaryGeneratedC<PERSON>umn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  OneToMany,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { CallLog } from './call-log.entity';
import { PushNotification } from './push-notification.entity';

export enum DeviceType {
  ANDROID = 'android',
  IOS = 'ios',
}

export enum DeviceStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended',
}

@Entity('devices')
@Index(['deviceId'], { unique: true })
@Index(['pushToken'])
@Index(['sipUsername'])
export class Device {
  @ApiProperty({ description: 'Unique device identifier' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: 'Device identifier from client' })
  @Column({ name: 'device_id', unique: true })
  deviceId: string;

  @ApiProperty({ enum: DeviceType, description: 'Type of device' })
  @Column({
    type: 'enum',
    enum: DeviceType,
    name: 'device_type',
  })
  deviceType: DeviceType;

  @ApiProperty({ description: 'Device name or model' })
  @Column({ name: 'device_name', nullable: true })
  deviceName?: string;

  @ApiProperty({ description: 'Operating system version' })
  @Column({ name: 'os_version', nullable: true })
  osVersion?: string;

  @ApiProperty({ description: 'App version' })
  @Column({ name: 'app_version', nullable: true })
  appVersion?: string;

  @ApiProperty({ description: 'Push notification token' })
  @Column({ name: 'push_token', nullable: true })
  pushToken?: string;

  @ApiProperty({ description: 'SIP username for VoIP calls' })
  @Column({ name: 'sip_username', nullable: true })
  sipUsername?: string;

  @ApiProperty({ description: 'SIP password (encrypted)' })
  @Column({ name: 'sip_password', nullable: true })
  sipPassword?: string;

  @ApiProperty({ description: 'SIP domain' })
  @Column({ name: 'sip_domain', nullable: true })
  sipDomain?: string;

  @ApiProperty({ enum: DeviceStatus, description: 'Device status' })
  @Column({
    type: 'enum',
    enum: DeviceStatus,
    default: DeviceStatus.ACTIVE,
  })
  status: DeviceStatus;

  @ApiProperty({ description: 'Device capabilities (JSON)' })
  @Column({ type: 'jsonb', nullable: true })
  capabilities?: {
    supportsVideo?: boolean;
    supportedCodecs?: string[];
    maxBitrate?: number;
    supportsHold?: boolean;
    supportsTransfer?: boolean;
  };

  @ApiProperty({ description: 'Network information' })
  @Column({ type: 'jsonb', nullable: true, name: 'network_info' })
  networkInfo?: {
    ipAddress?: string;
    userAgent?: string;
    lastSeen?: Date;
    connectionType?: string;
  };

  @ApiProperty({ description: 'Paired device ID' })
  @Column({ name: 'paired_device_id', nullable: true })
  pairedDeviceId?: string;

  @ApiProperty({ type: () => Device, description: 'Paired device' })
  @ManyToOne(() => Device, { nullable: true })
  @JoinColumn({ name: 'paired_device_id' })
  pairedDevice?: Device;

  @ApiProperty({ description: 'Last activity timestamp' })
  @Column({ name: 'last_activity', type: 'timestamp', nullable: true })
  lastActivity?: Date;

  @ApiProperty({ description: 'Device registration timestamp' })
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  // Relations
  @OneToMany(() => CallLog, (callLog) => callLog.initiatorDevice)
  initiatedCalls: CallLog[];

  @OneToMany(() => CallLog, (callLog) => callLog.receiverDevice)
  receivedCalls: CallLog[];

  @OneToMany(() => PushNotification, (pushNotification) => pushNotification.device)
  pushNotifications: PushNotification[];

  // Virtual properties
  get isOnline(): boolean {
    if (!this.lastActivity) return false;
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    return this.lastActivity > fiveMinutesAgo;
  }

  get isPaired(): boolean {
    return !!this.pairedDeviceId;
  }

  // Methods
  updateLastActivity(): void {
    this.lastActivity = new Date();
  }

  canReceiveCalls(): boolean {
    return (
      this.status === DeviceStatus.ACTIVE &&
      this.isOnline &&
      !!this.pushToken &&
      !!this.sipUsername
    );
  }

  toSafeObject() {
    const { sipPassword, ...safeDevice } = this;
    return safeDevice;
  }
}
