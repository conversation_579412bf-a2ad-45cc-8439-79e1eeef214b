import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { Device } from './device.entity';

export enum PushNotificationType {
  VOIP_CALL = 'voip_call',
  CALL_ENDED = 'call_ended',
  DEVICE_PAIRED = 'device_paired',
  SYSTEM_MESSAGE = 'system_message',
}

export enum PushNotificationStatus {
  PENDING = 'pending',
  SENT = 'sent',
  DELIVERED = 'delivered',
  FAILED = 'failed',
  EXPIRED = 'expired',
}

@Entity('push_notifications')
@Index(['deviceId'])
@Index(['type'])
@Index(['status'])
@Index(['createdAt'])
export class PushNotification {
  @ApiProperty({ description: 'Unique push notification identifier' })
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @ApiProperty({ description: 'Target device ID' })
  @Column({ name: 'device_id' })
  deviceId: string;

  @ApiProperty({ enum: PushNotificationType, description: 'Notification type' })
  @Column({
    type: 'enum',
    enum: PushNotificationType,
  })
  type: PushNotificationType;

  @ApiProperty({ description: 'Notification title' })
  @Column()
  title: string;

  @ApiProperty({ description: 'Notification body/message' })
  @Column()
  body: string;

  @ApiProperty({ description: 'Push notification payload' })
  @Column({ type: 'jsonb' })
  payload: {
    // VoIP call specific
    caller_id?: string;
    caller_name?: string;
    call_uuid?: string;
    server_info?: {
      sip_server?: string;
      port?: number;
      transport?: string;
    };
    
    // General payload
    [key: string]: any;
  };

  @ApiProperty({ enum: PushNotificationStatus, description: 'Notification status' })
  @Column({
    type: 'enum',
    enum: PushNotificationStatus,
    default: PushNotificationStatus.PENDING,
  })
  status: PushNotificationStatus;

  @ApiProperty({ description: 'Apple Push Notification ID' })
  @Column({ name: 'apns_id', nullable: true })
  apnsId?: string;

  @ApiProperty({ description: 'Push notification priority (1-10)' })
  @Column({ type: 'integer', default: 10 })
  priority: number;

  @ApiProperty({ description: 'Notification expiration timestamp' })
  @Column({ name: 'expires_at', type: 'timestamp', nullable: true })
  expiresAt?: Date;

  @ApiProperty({ description: 'Timestamp when notification was sent' })
  @Column({ name: 'sent_at', type: 'timestamp', nullable: true })
  sentAt?: Date;

  @ApiProperty({ description: 'Timestamp when notification was delivered' })
  @Column({ name: 'delivered_at', type: 'timestamp', nullable: true })
  deliveredAt?: Date;

  @ApiProperty({ description: 'Number of retry attempts' })
  @Column({ name: 'retry_count', type: 'integer', default: 0 })
  retryCount: number;

  @ApiProperty({ description: 'Maximum retry attempts' })
  @Column({ name: 'max_retries', type: 'integer', default: 3 })
  maxRetries: number;

  @ApiProperty({ description: 'Error information if failed' })
  @Column({ type: 'jsonb', nullable: true, name: 'error_info' })
  errorInfo?: {
    code?: number;
    message?: string;
    details?: string;
    apnsError?: string;
  };

  @ApiProperty({ description: 'Response time in milliseconds' })
  @Column({ name: 'response_time', type: 'integer', nullable: true })
  responseTime?: number;

  @ApiProperty({ description: 'Push notification creation timestamp' })
  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  // Relations
  @ApiProperty({ type: () => Device, description: 'Target device' })
  @ManyToOne(() => Device, (device) => device.pushNotifications)
  @JoinColumn({ name: 'device_id' })
  device: Device;

  // Virtual properties
  get isExpired(): boolean {
    return this.expiresAt ? new Date() > this.expiresAt : false;
  }

  get canRetry(): boolean {
    return (
      this.status === PushNotificationStatus.FAILED &&
      this.retryCount < this.maxRetries &&
      !this.isExpired
    );
  }

  get age(): number {
    return Date.now() - this.createdAt.getTime();
  }

  // Methods
  markAsSent(apnsId?: string): void {
    this.status = PushNotificationStatus.SENT;
    this.sentAt = new Date();
    if (apnsId) {
      this.apnsId = apnsId;
    }
  }

  markAsDelivered(): void {
    this.status = PushNotificationStatus.DELIVERED;
    this.deliveredAt = new Date();
  }

  markAsFailed(errorInfo: any): void {
    this.status = PushNotificationStatus.FAILED;
    this.errorInfo = errorInfo;
    this.retryCount += 1;
  }

  markAsExpired(): void {
    this.status = PushNotificationStatus.EXPIRED;
  }

  setResponseTime(startTime: number): void {
    this.responseTime = Date.now() - startTime;
  }

  // Static factory methods
  static createVoIPCall(
    deviceId: string,
    callerId: string,
    callerName?: string,
    callUuid?: string,
    serverInfo?: any,
  ): Partial<PushNotification> {
    return {
      deviceId,
      type: PushNotificationType.VOIP_CALL,
      title: 'Incoming Call',
      body: `Incoming call from ${callerName || callerId}`,
      payload: {
        caller_id: callerId,
        caller_name: callerName,
        call_uuid: callUuid,
        server_info: serverInfo,
      },
      priority: 10,
      expiresAt: new Date(Date.now() + 30 * 1000), // 30 seconds
    };
  }

  static createCallEnded(
    deviceId: string,
    callerId: string,
    duration: number,
  ): Partial<PushNotification> {
    return {
      deviceId,
      type: PushNotificationType.CALL_ENDED,
      title: 'Call Ended',
      body: `Call with ${callerId} ended (${duration}s)`,
      payload: {
        caller_id: callerId,
        duration,
      },
      priority: 5,
    };
  }

  static createDevicePaired(
    deviceId: string,
    pairedDeviceName: string,
  ): Partial<PushNotification> {
    return {
      deviceId,
      type: PushNotificationType.DEVICE_PAIRED,
      title: 'Device Paired',
      body: `Successfully paired with ${pairedDeviceName}`,
      payload: {
        paired_device_name: pairedDeviceName,
      },
      priority: 5,
    };
  }
}
