import { IsString, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ptional, IsObject, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { DeviceType } from '../../database/entities/device.entity';

class DeviceCapabilitiesDto {
  @ApiPropertyOptional({ description: 'Supports video calls' })
  @IsOptional()
  supportsVideo?: boolean;

  @ApiPropertyOptional({ description: 'Supported audio codecs', type: [String] })
  @IsOptional()
  supportedCodecs?: string[];

  @ApiPropertyOptional({ description: 'Maximum bitrate in kbps' })
  @IsOptional()
  maxBitrate?: number;

  @ApiPropertyOptional({ description: 'Supports call hold' })
  @IsOptional()
  supportsHold?: boolean;

  @ApiPropertyOptional({ description: 'Supports call transfer' })
  @IsOptional()
  supportsTransfer?: boolean;
}

class NetworkInfoDto {
  @ApiPropertyOptional({ description: 'Device IP address' })
  @IsOptional()
  @IsString()
  ipAddress?: string;

  @ApiPropertyOptional({ description: 'User agent string' })
  @IsOptional()
  @IsString()
  userAgent?: string;

  @ApiPropertyOptional({ description: 'Connection type (wifi, cellular, etc.)' })
  @IsOptional()
  @IsString()
  connectionType?: string;
}

export class RegisterDeviceDto {
  @ApiProperty({ description: 'Unique device identifier' })
  @IsString()
  deviceId: string;

  @ApiProperty({ enum: DeviceType, description: 'Type of device' })
  @IsEnum(DeviceType)
  deviceType: DeviceType;

  @ApiPropertyOptional({ description: 'Device name or model' })
  @IsOptional()
  @IsString()
  deviceName?: string;

  @ApiPropertyOptional({ description: 'Operating system version' })
  @IsOptional()
  @IsString()
  osVersion?: string;

  @ApiPropertyOptional({ description: 'App version' })
  @IsOptional()
  @IsString()
  appVersion?: string;

  @ApiPropertyOptional({ description: 'Push notification token' })
  @IsOptional()
  @IsString()
  pushToken?: string;

  @ApiPropertyOptional({ description: 'SIP username for VoIP calls' })
  @IsOptional()
  @IsString()
  sipUsername?: string;

  @ApiPropertyOptional({ description: 'SIP password' })
  @IsOptional()
  @IsString()
  sipPassword?: string;

  @ApiPropertyOptional({ description: 'SIP domain' })
  @IsOptional()
  @IsString()
  sipDomain?: string;

  @ApiPropertyOptional({ type: DeviceCapabilitiesDto, description: 'Device capabilities' })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => DeviceCapabilitiesDto)
  capabilities?: DeviceCapabilitiesDto;

  @ApiPropertyOptional({ type: NetworkInfoDto, description: 'Network information' })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => NetworkInfoDto)
  networkInfo?: NetworkInfoDto;
}
