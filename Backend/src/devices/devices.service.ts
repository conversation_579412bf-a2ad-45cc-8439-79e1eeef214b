import { Injectable, NotFoundException, ConflictException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, FindOptionsWhere } from 'typeorm';
import { Device, DeviceType, DeviceStatus } from '../database/entities/device.entity';
import { RegisterDeviceDto } from './dto/register-device.dto';
import { UpdateDeviceDto } from './dto/update-device.dto';
import { PairDevicesDto } from './dto/pair-devices.dto';
import * as bcrypt from 'bcrypt';

@Injectable()
export class DevicesService {
  private readonly logger = new Logger(DevicesService.name);

  constructor(
    @InjectRepository(Device)
    private readonly deviceRepository: Repository<Device>,
  ) {}

  /**
   * Register a new device or update existing one
   */
  async registerDevice(registerDeviceDto: RegisterDeviceDto): Promise<Device> {
    const { deviceId, deviceType, pushToken, sipUsername, sipPassword, ...deviceData } = registerDeviceDto;

    // Check if device already exists
    let device = await this.deviceRepository.findOne({
      where: { deviceId },
    });

    if (device) {
      // Update existing device
      this.logger.log(`Updating existing device: ${deviceId}`);
      
      // Update push token if provided
      if (pushToken) {
        device.pushToken = pushToken;
      }

      // Update SIP credentials if provided
      if (sipUsername) {
        device.sipUsername = sipUsername;
      }

      if (sipPassword) {
        device.sipPassword = await this.hashPassword(sipPassword);
      }

      // Update other fields
      Object.assign(device, deviceData);
      device.updateLastActivity();
      device.status = DeviceStatus.ACTIVE;

    } else {
      // Create new device
      this.logger.log(`Registering new device: ${deviceId} (${deviceType})`);
      
      device = this.deviceRepository.create({
        deviceId,
        deviceType,
        pushToken,
        sipUsername,
        sipPassword: sipPassword ? await this.hashPassword(sipPassword) : undefined,
        ...deviceData,
        status: DeviceStatus.ACTIVE,
        lastActivity: new Date(),
      });
    }

    return await this.deviceRepository.save(device);
  }

  /**
   * Update device information
   */
  async updateDevice(deviceId: string, updateDeviceDto: UpdateDeviceDto): Promise<Device> {
    const device = await this.findByDeviceId(deviceId);

    // Update SIP password if provided
    if (updateDeviceDto.sipPassword) {
      updateDeviceDto.sipPassword = await this.hashPassword(updateDeviceDto.sipPassword);
    }

    Object.assign(device, updateDeviceDto);
    device.updateLastActivity();

    return await this.deviceRepository.save(device);
  }

  /**
   * Find device by device ID
   */
  async findByDeviceId(deviceId: string): Promise<Device> {
    const device = await this.deviceRepository.findOne({
      where: { deviceId },
      relations: ['pairedDevice'],
    });

    if (!device) {
      throw new NotFoundException(`Device with ID ${deviceId} not found`);
    }

    return device;
  }

  /**
   * Find device by database ID
   */
  async findById(id: string): Promise<Device> {
    const device = await this.deviceRepository.findOne({
      where: { id },
      relations: ['pairedDevice'],
    });

    if (!device) {
      throw new NotFoundException(`Device with ID ${id} not found`);
    }

    return device;
  }

  /**
   * Find device by SIP username
   */
  async findBySipUsername(sipUsername: string): Promise<Device | null> {
    return await this.deviceRepository.findOne({
      where: { sipUsername },
      relations: ['pairedDevice'],
    });
  }

  /**
   * Find device by push token
   */
  async findByPushToken(pushToken: string): Promise<Device | null> {
    return await this.deviceRepository.findOne({
      where: { pushToken },
      relations: ['pairedDevice'],
    });
  }

  /**
   * Get all devices with optional filtering
   */
  async findAll(filters?: {
    deviceType?: DeviceType;
    status?: DeviceStatus;
    isPaired?: boolean;
  }): Promise<Device[]> {
    const where: FindOptionsWhere<Device> = {};

    if (filters?.deviceType) {
      where.deviceType = filters.deviceType;
    }

    if (filters?.status) {
      where.status = filters.status;
    }

    const queryBuilder = this.deviceRepository.createQueryBuilder('device')
      .leftJoinAndSelect('device.pairedDevice', 'pairedDevice');

    if (Object.keys(where).length > 0) {
      queryBuilder.where(where);
    }

    if (filters?.isPaired !== undefined) {
      if (filters.isPaired) {
        queryBuilder.andWhere('device.pairedDeviceId IS NOT NULL');
      } else {
        queryBuilder.andWhere('device.pairedDeviceId IS NULL');
      }
    }

    return await queryBuilder.getMany();
  }

  /**
   * Pair two devices together
   */
  async pairDevices(pairDevicesDto: PairDevicesDto): Promise<{ android: Device; ios: Device }> {
    const { androidDeviceId, iosDeviceId } = pairDevicesDto;

    // Find both devices
    const androidDevice = await this.findByDeviceId(androidDeviceId);
    const iosDevice = await this.findByDeviceId(iosDeviceId);

    // Validate device types
    if (androidDevice.deviceType !== DeviceType.ANDROID) {
      throw new ConflictException(`Device ${androidDeviceId} is not an Android device`);
    }

    if (iosDevice.deviceType !== DeviceType.IOS) {
      throw new ConflictException(`Device ${iosDeviceId} is not an iOS device`);
    }

    // Check if devices are already paired
    if (androidDevice.isPaired || iosDevice.isPaired) {
      throw new ConflictException('One or both devices are already paired');
    }

    // Pair the devices
    androidDevice.pairedDeviceId = iosDevice.id;
    iosDevice.pairedDeviceId = androidDevice.id;

    await this.deviceRepository.save([androidDevice, iosDevice]);

    this.logger.log(`Paired devices: ${androidDeviceId} <-> ${iosDeviceId}`);

    return {
      android: androidDevice,
      ios: iosDevice,
    };
  }

  /**
   * Unpair devices
   */
  async unpairDevice(deviceId: string): Promise<Device> {
    const device = await this.findByDeviceId(deviceId);

    if (!device.isPaired) {
      throw new ConflictException('Device is not paired');
    }

    // Find paired device and unpair both
    if (device.pairedDeviceId) {
      const pairedDevice = await this.findById(device.pairedDeviceId);
      pairedDevice.pairedDeviceId = null;
      await this.deviceRepository.save(pairedDevice);
    }

    device.pairedDeviceId = null;
    await this.deviceRepository.save(device);

    this.logger.log(`Unpaired device: ${deviceId}`);

    return device;
  }

  /**
   * Update device push token
   */
  async updatePushToken(deviceId: string, pushToken: string): Promise<Device> {
    const device = await this.findByDeviceId(deviceId);
    device.pushToken = pushToken;
    device.updateLastActivity();

    return await this.deviceRepository.save(device);
  }

  /**
   * Update device last activity
   */
  async updateLastActivity(deviceId: string): Promise<void> {
    await this.deviceRepository.update(
      { deviceId },
      { lastActivity: new Date() }
    );
  }

  /**
   * Set device status
   */
  async setDeviceStatus(deviceId: string, status: DeviceStatus): Promise<Device> {
    const device = await this.findByDeviceId(deviceId);
    device.status = status;
    device.updateLastActivity();

    return await this.deviceRepository.save(device);
  }

  /**
   * Get device statistics
   */
  async getDeviceStats(): Promise<{
    total: number;
    android: number;
    ios: number;
    active: number;
    paired: number;
    online: number;
  }> {
    const [total, android, ios, active, paired] = await Promise.all([
      this.deviceRepository.count(),
      this.deviceRepository.count({ where: { deviceType: DeviceType.ANDROID } }),
      this.deviceRepository.count({ where: { deviceType: DeviceType.IOS } }),
      this.deviceRepository.count({ where: { status: DeviceStatus.ACTIVE } }),
      this.deviceRepository.createQueryBuilder('device')
        .where('device.pairedDeviceId IS NOT NULL')
        .getCount(),
    ]);

    // Count online devices (active in last 5 minutes)
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    const online = await this.deviceRepository
      .createQueryBuilder('device')
      .where('device.lastActivity > :fiveMinutesAgo', { fiveMinutesAgo })
      .andWhere('device.status = :status', { status: DeviceStatus.ACTIVE })
      .getCount();

    return {
      total,
      android,
      ios,
      active,
      paired,
      online,
    };
  }

  /**
   * Delete device
   */
  async deleteDevice(deviceId: string): Promise<void> {
    const device = await this.findByDeviceId(deviceId);

    // Unpair if paired
    if (device.isPaired) {
      await this.unpairDevice(deviceId);
    }

    await this.deviceRepository.remove(device);
    this.logger.log(`Deleted device: ${deviceId}`);
  }

  /**
   * Hash password
   */
  private async hashPassword(password: string): Promise<string> {
    const saltRounds = 10;
    return await bcrypt.hash(password, saltRounds);
  }

  /**
   * Verify password
   */
  async verifyPassword(device: Device, password: string): Promise<boolean> {
    if (!device.sipPassword) {
      return false;
    }
    return await bcrypt.compare(password, device.sipPassword);
  }
}
