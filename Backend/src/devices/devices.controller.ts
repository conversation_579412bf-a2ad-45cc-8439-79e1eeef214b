import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { DevicesService } from './devices.service';
import { RegisterDeviceDto } from './dto/register-device.dto';
import { UpdateDeviceDto } from './dto/update-device.dto';
import { PairDevicesDto } from './dto/pair-devices.dto';
import { Device, DeviceType, DeviceStatus } from '../database/entities/device.entity';
import { Public } from '../auth/decorators/public.decorator';

@ApiTags('Devices')
@ApiBearerAuth()
@Controller('devices')
export class DevicesController {
  constructor(private readonly devicesService: DevicesService) {}

  @Public()
  @Post('register')
  @ApiOperation({ summary: 'Register a new device or update existing one' })
  @ApiResponse({ status: 201, description: 'Device registered successfully', type: Device })
  @ApiResponse({ status: 400, description: 'Invalid device data' })
  async register(@Body() registerDeviceDto: RegisterDeviceDto): Promise<Device> {
    return await this.devicesService.registerDevice(registerDeviceDto);
  }

  @Get()
  @ApiOperation({ summary: 'Get all devices with optional filtering' })
  @ApiQuery({ name: 'deviceType', enum: DeviceType, required: false })
  @ApiQuery({ name: 'status', enum: DeviceStatus, required: false })
  @ApiQuery({ name: 'isPaired', type: Boolean, required: false })
  @ApiResponse({ status: 200, description: 'Devices retrieved successfully', type: [Device] })
  async findAll(
    @Query('deviceType') deviceType?: DeviceType,
    @Query('status') status?: DeviceStatus,
    @Query('isPaired') isPaired?: boolean,
  ): Promise<Device[]> {
    return await this.devicesService.findAll({
      deviceType,
      status,
      isPaired,
    });
  }

  @Get('stats')
  @ApiOperation({ summary: 'Get device statistics' })
  @ApiResponse({ status: 200, description: 'Device statistics retrieved successfully' })
  async getStats() {
    return await this.devicesService.getDeviceStats();
  }

  @Get(':deviceId')
  @ApiOperation({ summary: 'Get device by device ID' })
  @ApiResponse({ status: 200, description: 'Device found', type: Device })
  @ApiResponse({ status: 404, description: 'Device not found' })
  async findOne(@Param('deviceId') deviceId: string): Promise<Device> {
    return await this.devicesService.findByDeviceId(deviceId);
  }

  @Put(':deviceId')
  @ApiOperation({ summary: 'Update device information' })
  @ApiResponse({ status: 200, description: 'Device updated successfully', type: Device })
  @ApiResponse({ status: 404, description: 'Device not found' })
  async update(
    @Param('deviceId') deviceId: string,
    @Body() updateDeviceDto: UpdateDeviceDto,
  ): Promise<Device> {
    return await this.devicesService.updateDevice(deviceId, updateDeviceDto);
  }

  @Post('pair')
  @ApiOperation({ summary: 'Pair Android and iOS devices' })
  @ApiResponse({ status: 200, description: 'Devices paired successfully' })
  @ApiResponse({ status: 400, description: 'Invalid pairing request' })
  @ApiResponse({ status: 409, description: 'Devices already paired or invalid types' })
  async pairDevices(@Body() pairDevicesDto: PairDevicesDto) {
    return await this.devicesService.pairDevices(pairDevicesDto);
  }

  @Post(':deviceId/unpair')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Unpair device' })
  @ApiResponse({ status: 200, description: 'Device unpaired successfully', type: Device })
  @ApiResponse({ status: 404, description: 'Device not found' })
  @ApiResponse({ status: 409, description: 'Device is not paired' })
  async unpairDevice(@Param('deviceId') deviceId: string): Promise<Device> {
    return await this.devicesService.unpairDevice(deviceId);
  }

  @Put(':deviceId/push-token')
  @ApiOperation({ summary: 'Update device push token' })
  @ApiResponse({ status: 200, description: 'Push token updated successfully', type: Device })
  @ApiResponse({ status: 404, description: 'Device not found' })
  async updatePushToken(
    @Param('deviceId') deviceId: string,
    @Body() body: { pushToken: string },
  ): Promise<Device> {
    return await this.devicesService.updatePushToken(deviceId, body.pushToken);
  }

  @Put(':deviceId/status')
  @ApiOperation({ summary: 'Update device status' })
  @ApiResponse({ status: 200, description: 'Device status updated successfully', type: Device })
  @ApiResponse({ status: 404, description: 'Device not found' })
  async updateStatus(
    @Param('deviceId') deviceId: string,
    @Body() body: { status: DeviceStatus },
  ): Promise<Device> {
    return await this.devicesService.setDeviceStatus(deviceId, body.status);
  }

  @Post(':deviceId/activity')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Update device last activity' })
  @ApiResponse({ status: 204, description: 'Activity updated successfully' })
  @ApiResponse({ status: 404, description: 'Device not found' })
  async updateActivity(@Param('deviceId') deviceId: string): Promise<void> {
    await this.devicesService.updateLastActivity(deviceId);
  }

  @Delete(':deviceId')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete device' })
  @ApiResponse({ status: 204, description: 'Device deleted successfully' })
  @ApiResponse({ status: 404, description: 'Device not found' })
  async delete(@Param('deviceId') deviceId: string): Promise<void> {
    await this.devicesService.deleteDevice(deviceId);
  }
}
