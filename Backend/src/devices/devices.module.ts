import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DevicesController } from './devices.controller';
import { DevicesService } from './devices.service';
import { Device } from '../database/entities/device.entity';
import { CallLog } from '../database/entities/call-log.entity';
import { PushNotification } from '../database/entities/push-notification.entity';

@Module({
  imports: [
    TypeOrmModule.forFeature([Device, CallLog, PushNotification]),
  ],
  controllers: [DevicesController],
  providers: [DevicesService],
  exports: [DevicesService],
})
export class DevicesModule {}
