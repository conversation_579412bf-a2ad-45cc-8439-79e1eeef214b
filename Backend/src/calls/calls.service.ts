import { Injectable, Logger, NotFoundException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { CallLog, CallDirection, CallStatus, CallEndReason } from '../database/entities/call-log.entity';
import { Device, DeviceType } from '../database/entities/device.entity';
import { DevicesService } from '../devices/devices.service';
import { PushService } from '../push/push.service';
import { InitiateCallDto } from './dto/initiate-call.dto';
import { UpdateCallDto } from './dto/update-call.dto';

@Injectable()
export class CallsService {
  private readonly logger = new Logger(CallsService.name);
  private readonly activeCalls = new Map<string, CallLog>();

  constructor(
    @InjectRepository(CallLog)
    private readonly callLogRepository: Repository<CallLog>,
    private readonly devicesService: DevicesService,
    private readonly pushService: PushService,
  ) {}

  /**
   * Initiate a call from Android to iOS
   */
  async initiateCall(initiateCallDto: InitiateCallDto): Promise<CallLog> {
    const { androidDeviceId, callerId, callerName } = initiateCallDto;

    this.logger.log(`Initiating call from ${androidDeviceId} (${callerId})`);

    // Find the Android device
    const androidDevice = await this.devicesService.findByDeviceId(androidDeviceId);

    if (androidDevice.deviceType !== DeviceType.ANDROID) {
      throw new ConflictException('Initiator must be an Android device');
    }

    if (!androidDevice.isPaired) {
      throw new ConflictException('Android device is not paired with an iOS device');
    }

    // Find the paired iOS device
    const iosDevice = await this.devicesService.findById(androidDevice.pairedDeviceId);

    if (!iosDevice.canReceiveCalls()) {
      throw new ConflictException('iOS device cannot receive calls (offline or inactive)');
    }

    // Generate unique call ID
    const callId = uuidv4();
    const callUuid = uuidv4();

    // Create call log
    const callLog = this.callLogRepository.create({
      callId,
      initiatorDeviceId: androidDevice.id,
      receiverDeviceId: iosDevice.id,
      callerId,
      callerName,
      direction: CallDirection.OUTBOUND, // From Android perspective
      status: CallStatus.INITIATED,
      startTime: new Date(),
    });

    await this.callLogRepository.save(callLog);

    // Add to active calls
    this.activeCalls.set(callId, callLog);

    // Send VoIP push notification to iOS device
    try {
      await this.pushService.sendVoIPCall(
        iosDevice.deviceId,
        callerId,
        callerName,
        callUuid,
        {
          sip_server: process.env.SIP_SERVER_HOST || 'localhost',
          port: parseInt(process.env.SIP_SERVER_PORT, 10) || 5060,
          transport: process.env.SIP_TRANSPORT || 'UDP',
        },
      );

      // Update call status to ringing
      callLog.status = CallStatus.RINGING;
      await this.callLogRepository.save(callLog);

      this.logger.log(`VoIP push sent for call ${callId}`);

    } catch (error) {
      this.logger.error(`Failed to send VoIP push for call ${callId}:`, error);
      
      // Mark call as failed
      callLog.setFailed(CallEndReason.SERVER_ERROR, {
        message: 'Failed to send VoIP push notification',
        details: error.message,
      });
      
      await this.callLogRepository.save(callLog);
      this.activeCalls.delete(callId);
      
      throw error;
    }

    return callLog;
  }

  /**
   * Update call status
   */
  async updateCall(callId: string, updateCallDto: UpdateCallDto): Promise<CallLog> {
    const callLog = await this.findCallById(callId);

    const { status, qualityMetrics, networkStats, sipDetails, endReason } = updateCallDto;

    // Update status
    if (status) {
      switch (status) {
        case CallStatus.ANSWERED:
          callLog.setAnswered();
          break;
        case CallStatus.CONNECTED:
          callLog.setConnected();
          break;
        case CallStatus.ENDED:
          callLog.setEnded(endReason || CallEndReason.NORMAL);
          this.activeCalls.delete(callId);
          break;
        case CallStatus.FAILED:
          callLog.setFailed(endReason || CallEndReason.SERVER_ERROR);
          this.activeCalls.delete(callId);
          break;
        default:
          callLog.status = status;
      }
    }

    // Update metrics
    if (qualityMetrics) {
      callLog.qualityMetrics = { ...callLog.qualityMetrics, ...qualityMetrics };
    }

    if (networkStats) {
      callLog.networkStats = { ...callLog.networkStats, ...networkStats };
    }

    if (sipDetails) {
      callLog.sipDetails = { ...callLog.sipDetails, ...sipDetails };
    }

    await this.callLogRepository.save(callLog);

    this.logger.log(`Updated call ${callId} status to ${callLog.status}`);

    return callLog;
  }

  /**
   * Answer a call
   */
  async answerCall(callId: string): Promise<CallLog> {
    const callLog = await this.findCallById(callId);

    if (callLog.status !== CallStatus.RINGING) {
      throw new ConflictException('Call is not in ringing state');
    }

    callLog.setAnswered();
    await this.callLogRepository.save(callLog);

    this.logger.log(`Call ${callId} answered`);

    return callLog;
  }

  /**
   * End a call
   */
  async endCall(callId: string, reason: CallEndReason = CallEndReason.NORMAL): Promise<CallLog> {
    const callLog = await this.findCallById(callId);

    if (!callLog.isActive) {
      throw new ConflictException('Call is not active');
    }

    callLog.setEnded(reason);
    await this.callLogRepository.save(callLog);

    // Remove from active calls
    this.activeCalls.delete(callId);

    // Send call ended notification if call was answered
    if (callLog.wasAnswered) {
      try {
        const initiatorDevice = await this.devicesService.findById(callLog.initiatorDeviceId);
        await this.pushService.sendCallEnded(
          initiatorDevice.deviceId,
          callLog.callerId,
          callLog.duration,
        );
      } catch (error) {
        this.logger.error(`Failed to send call ended notification for call ${callId}:`, error);
      }
    }

    this.logger.log(`Call ${callId} ended with reason: ${reason}`);

    return callLog;
  }

  /**
   * Get call by ID
   */
  async findCallById(callId: string): Promise<CallLog> {
    const callLog = await this.callLogRepository.findOne({
      where: { callId },
      relations: ['initiatorDevice', 'receiverDevice'],
    });

    if (!callLog) {
      throw new NotFoundException(`Call with ID ${callId} not found`);
    }

    return callLog;
  }

  /**
   * Get active calls
   */
  async getActiveCalls(): Promise<CallLog[]> {
    return await this.callLogRepository.find({
      where: {
        status: CallStatus.INITIATED || CallStatus.RINGING || CallStatus.ANSWERED || CallStatus.CONNECTED,
      },
      relations: ['initiatorDevice', 'receiverDevice'],
      order: { startTime: 'DESC' },
    });
  }

  /**
   * Get call history for a device
   */
  async getDeviceCallHistory(
    deviceId: string,
    limit: number = 50,
    offset: number = 0,
  ): Promise<{ calls: CallLog[]; total: number }> {
    const device = await this.devicesService.findByDeviceId(deviceId);

    const [calls, total] = await this.callLogRepository.findAndCount({
      where: [
        { initiatorDeviceId: device.id },
        { receiverDeviceId: device.id },
      ],
      relations: ['initiatorDevice', 'receiverDevice'],
      order: { startTime: 'DESC' },
      take: limit,
      skip: offset,
    });

    return { calls, total };
  }

  /**
   * Get call statistics
   */
  async getCallStats(deviceId?: string): Promise<{
    total: number;
    answered: number;
    missed: number;
    failed: number;
    averageDuration: number;
    totalDuration: number;
  }> {
    let queryBuilder = this.callLogRepository.createQueryBuilder('call');

    if (deviceId) {
      const device = await this.devicesService.findByDeviceId(deviceId);
      queryBuilder = queryBuilder.where(
        'call.initiatorDeviceId = :deviceId OR call.receiverDeviceId = :deviceId',
        { deviceId: device.id },
      );
    }

    const calls = await queryBuilder.getMany();

    const total = calls.length;
    const answered = calls.filter(call => call.wasAnswered).length;
    const missed = calls.filter(call => call.status === CallStatus.MISSED).length;
    const failed = calls.filter(call => call.status === CallStatus.FAILED).length;
    const totalDuration = calls.reduce((sum, call) => sum + call.duration, 0);
    const averageDuration = answered > 0 ? totalDuration / answered : 0;

    return {
      total,
      answered,
      missed,
      failed,
      averageDuration,
      totalDuration,
    };
  }

  /**
   * Get call statistics by date range
   */
  async getCallStatsByDateRange(
    startDate: Date,
    endDate: Date,
    deviceId?: string,
  ): Promise<{
    daily: Array<{
      date: string;
      total: number;
      answered: number;
      missed: number;
      duration: number;
    }>;
    summary: {
      total: number;
      answered: number;
      missed: number;
      averageDuration: number;
    };
  }> {
    let queryBuilder = this.callLogRepository.createQueryBuilder('call')
      .where('call.startTime BETWEEN :startDate AND :endDate', { startDate, endDate });

    if (deviceId) {
      const device = await this.devicesService.findByDeviceId(deviceId);
      queryBuilder = queryBuilder.andWhere(
        'call.initiatorDeviceId = :deviceId OR call.receiverDeviceId = :deviceId',
        { deviceId: device.id },
      );
    }

    const calls = await queryBuilder.getMany();

    // Group by date
    const dailyStats = new Map<string, {
      total: number;
      answered: number;
      missed: number;
      duration: number;
    }>();

    calls.forEach(call => {
      const date = call.startTime.toISOString().split('T')[0];
      
      if (!dailyStats.has(date)) {
        dailyStats.set(date, { total: 0, answered: 0, missed: 0, duration: 0 });
      }

      const stats = dailyStats.get(date);
      stats.total++;
      
      if (call.wasAnswered) {
        stats.answered++;
        stats.duration += call.duration;
      } else if (call.status === CallStatus.MISSED) {
        stats.missed++;
      }
    });

    // Convert to array
    const daily = Array.from(dailyStats.entries()).map(([date, stats]) => ({
      date,
      ...stats,
    }));

    // Calculate summary
    const total = calls.length;
    const answered = calls.filter(call => call.wasAnswered).length;
    const missed = calls.filter(call => call.status === CallStatus.MISSED).length;
    const totalDuration = calls.reduce((sum, call) => sum + call.duration, 0);
    const averageDuration = answered > 0 ? totalDuration / answered : 0;

    return {
      daily,
      summary: {
        total,
        answered,
        missed,
        averageDuration,
      },
    };
  }

  /**
   * Clean up old call logs
   */
  async cleanupOldCalls(daysToKeep: number = 90): Promise<number> {
    const cutoffDate = new Date(Date.now() - daysToKeep * 24 * 60 * 60 * 1000);

    const result = await this.callLogRepository
      .createQueryBuilder()
      .delete()
      .where('startTime < :cutoffDate', { cutoffDate })
      .execute();

    this.logger.log(`Cleaned up ${result.affected} old call logs`);

    return result.affected || 0;
  }
}
