import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery, ApiBearerAuth } from '@nestjs/swagger';
import { CallsService } from './calls.service';
import { InitiateCallDto } from './dto/initiate-call.dto';
import { UpdateCallDto } from './dto/update-call.dto';
import { CallLog, CallEndReason } from '../database/entities/call-log.entity';

@ApiTags('Calls')
@ApiBearerAuth()
@Controller('calls')
export class CallsController {
  constructor(private readonly callsService: CallsService) {}

  @Post('initiate')
  @ApiOperation({ summary: 'Initiate a call from Android to iOS' })
  @ApiResponse({ status: 201, description: 'Call initiated successfully', type: CallLog })
  @ApiResponse({ status: 400, description: 'Invalid call request' })
  @ApiResponse({ status: 409, description: 'Device not paired or unavailable' })
  async initiateCall(@Body() initiateCallDto: InitiateCallDto): Promise<CallLog> {
    return await this.callsService.initiateCall(initiateCallDto);
  }

  @Put(':callId')
  @ApiOperation({ summary: 'Update call status and metrics' })
  @ApiResponse({ status: 200, description: 'Call updated successfully', type: CallLog })
  @ApiResponse({ status: 404, description: 'Call not found' })
  async updateCall(
    @Param('callId') callId: string,
    @Body() updateCallDto: UpdateCallDto,
  ): Promise<CallLog> {
    return await this.callsService.updateCall(callId, updateCallDto);
  }

  @Post(':callId/answer')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Answer a call' })
  @ApiResponse({ status: 200, description: 'Call answered successfully', type: CallLog })
  @ApiResponse({ status: 404, description: 'Call not found' })
  @ApiResponse({ status: 409, description: 'Call is not in ringing state' })
  async answerCall(@Param('callId') callId: string): Promise<CallLog> {
    return await this.callsService.answerCall(callId);
  }

  @Post(':callId/end')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'End a call' })
  @ApiResponse({ status: 200, description: 'Call ended successfully', type: CallLog })
  @ApiResponse({ status: 404, description: 'Call not found' })
  @ApiResponse({ status: 409, description: 'Call is not active' })
  async endCall(
    @Param('callId') callId: string,
    @Body() body: { reason?: CallEndReason },
  ): Promise<CallLog> {
    return await this.callsService.endCall(callId, body.reason);
  }

  @Get('active')
  @ApiOperation({ summary: 'Get all active calls' })
  @ApiResponse({ status: 200, description: 'Active calls retrieved successfully', type: [CallLog] })
  async getActiveCalls(): Promise<CallLog[]> {
    return await this.callsService.getActiveCalls();
  }

  @Get('history/:deviceId')
  @ApiOperation({ summary: 'Get call history for a device' })
  @ApiQuery({ name: 'limit', type: Number, required: false, description: 'Number of calls to return (default: 50)' })
  @ApiQuery({ name: 'offset', type: Number, required: false, description: 'Number of calls to skip (default: 0)' })
  @ApiResponse({ status: 200, description: 'Call history retrieved successfully' })
  @ApiResponse({ status: 404, description: 'Device not found' })
  async getDeviceCallHistory(
    @Param('deviceId') deviceId: string,
    @Query('limit') limit?: number,
    @Query('offset') offset?: number,
  ) {
    return await this.callsService.getDeviceCallHistory(
      deviceId,
      limit || 50,
      offset || 0,
    );
  }

  @Get('stats')
  @ApiOperation({ summary: 'Get call statistics' })
  @ApiQuery({ name: 'deviceId', type: String, required: false, description: 'Filter by device ID' })
  @ApiResponse({ status: 200, description: 'Call statistics retrieved successfully' })
  async getCallStats(@Query('deviceId') deviceId?: string) {
    return await this.callsService.getCallStats(deviceId);
  }

  @Get('stats/date-range')
  @ApiOperation({ summary: 'Get call statistics by date range' })
  @ApiQuery({ name: 'startDate', type: String, required: true, description: 'Start date (ISO string)' })
  @ApiQuery({ name: 'endDate', type: String, required: true, description: 'End date (ISO string)' })
  @ApiQuery({ name: 'deviceId', type: String, required: false, description: 'Filter by device ID' })
  @ApiResponse({ status: 200, description: 'Call statistics by date range retrieved successfully' })
  async getCallStatsByDateRange(
    @Query('startDate') startDate: string,
    @Query('endDate') endDate: string,
    @Query('deviceId') deviceId?: string,
  ) {
    return await this.callsService.getCallStatsByDateRange(
      new Date(startDate),
      new Date(endDate),
      deviceId,
    );
  }

  @Get(':callId')
  @ApiOperation({ summary: 'Get call by ID' })
  @ApiResponse({ status: 200, description: 'Call found', type: CallLog })
  @ApiResponse({ status: 404, description: 'Call not found' })
  async findOne(@Param('callId') callId: string): Promise<CallLog> {
    return await this.callsService.findCallById(callId);
  }
}
