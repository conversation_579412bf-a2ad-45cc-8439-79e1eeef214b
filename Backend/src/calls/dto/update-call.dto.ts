import { IsEnum, IsOptional, IsObject, IsString } from 'class-validator';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { CallStatus, CallEndReason } from '../../database/entities/call-log.entity';

export class QualityMetricsDto {
  @ApiPropertyOptional({ description: 'Jitter in milliseconds' })
  @IsOptional()
  jitter?: number;

  @ApiPropertyOptional({ description: 'Packet loss percentage' })
  @IsOptional()
  packetLoss?: number;

  @ApiPropertyOptional({ description: 'Round trip time in milliseconds' })
  @IsOptional()
  roundTripTime?: number;

  @ApiPropertyOptional({ description: 'Audio level in dB' })
  @IsOptional()
  audioLevel?: number;

  @ApiPropertyOptional({ enum: ['good', 'fair', 'poor'], description: 'Quality score' })
  @IsOptional()
  qualityScore?: 'good' | 'fair' | 'poor';
}

export class NetworkStatsDto {
  @ApiPropertyOptional({ description: 'Bytes received' })
  @IsOptional()
  bytesReceived?: number;

  @ApiPropertyOptional({ description: 'Bytes sent' })
  @IsOptional()
  bytesSent?: number;

  @ApiPropertyOptional({ description: 'Packets received' })
  @IsOptional()
  packetsReceived?: number;

  @ApiPropertyOptional({ description: 'Packets sent' })
  @IsOptional()
  packetsSent?: number;

  @ApiPropertyOptional({ description: 'Packets lost' })
  @IsOptional()
  packetsLost?: number;
}

export class SipDetailsDto {
  @ApiPropertyOptional({ description: 'SIP Call-ID' })
  @IsOptional()
  @IsString()
  callId?: string;

  @ApiPropertyOptional({ description: 'From tag' })
  @IsOptional()
  @IsString()
  fromTag?: string;

  @ApiPropertyOptional({ description: 'To tag' })
  @IsOptional()
  @IsString()
  toTag?: string;

  @ApiPropertyOptional({ description: 'Branch parameter' })
  @IsOptional()
  @IsString()
  branch?: string;

  @ApiPropertyOptional({ description: 'User agent' })
  @IsOptional()
  @IsString()
  userAgent?: string;
}

export class UpdateCallDto {
  @ApiPropertyOptional({ enum: CallStatus, description: 'Call status' })
  @IsOptional()
  @IsEnum(CallStatus)
  status?: CallStatus;

  @ApiPropertyOptional({ enum: CallEndReason, description: 'Call end reason' })
  @IsOptional()
  @IsEnum(CallEndReason)
  endReason?: CallEndReason;

  @ApiPropertyOptional({ description: 'Audio codec used' })
  @IsOptional()
  @IsString()
  codec?: string;

  @ApiPropertyOptional({ type: QualityMetricsDto, description: 'Call quality metrics' })
  @IsOptional()
  @IsObject()
  qualityMetrics?: QualityMetricsDto;

  @ApiPropertyOptional({ type: NetworkStatsDto, description: 'Network statistics' })
  @IsOptional()
  @IsObject()
  networkStats?: NetworkStatsDto;

  @ApiPropertyOptional({ type: SipDetailsDto, description: 'SIP call details' })
  @IsOptional()
  @IsObject()
  sipDetails?: SipDetailsDto;
}
