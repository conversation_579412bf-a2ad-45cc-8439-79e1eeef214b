import { IsString, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class InitiateCallDto {
  @ApiProperty({ description: 'Android device ID initiating the call' })
  @IsString()
  androidDeviceId: string;

  @ApiProperty({ description: 'Caller phone number or ID' })
  @IsString()
  callerId: string;

  @ApiPropertyOptional({ description: 'Caller display name' })
  @IsOptional()
  @IsString()
  callerName?: string;
}
