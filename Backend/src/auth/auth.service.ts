import { Injectable, UnauthorizedException, Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import { DevicesService } from '../devices/devices.service';
import { Device } from '../database/entities/device.entity';

export interface JwtPayload {
  sub: string; // device ID
  deviceId: string;
  deviceType: string;
  iat?: number;
  exp?: number;
}

export interface AuthResult {
  access_token: string;
  refresh_token?: string;
  device: Omit<Device, 'sipPassword'>;
  expires_in: number;
}

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly devicesService: DevicesService,
  ) {}

  /**
   * Authenticate device with device ID and optional SIP credentials
   */
  async authenticateDevice(
    deviceId: string,
    sipUsername?: string,
    sipPassword?: string,
  ): Promise<AuthResult> {
    try {
      // Find device
      const device = await this.devicesService.findByDeviceId(deviceId);

      // Verify SIP credentials if provided
      if (sipUsername && sipPassword) {
        if (device.sipUsername !== sipUsername) {
          throw new UnauthorizedException('Invalid SIP username');
        }

        const isValidPassword = await this.devicesService.verifyPassword(device, sipPassword);
        if (!isValidPassword) {
          throw new UnauthorizedException('Invalid SIP password');
        }
      }

      // Update last activity
      await this.devicesService.updateLastActivity(deviceId);

      // Generate tokens
      const payload: JwtPayload = {
        sub: device.id,
        deviceId: device.deviceId,
        deviceType: device.deviceType,
      };

      const accessToken = this.jwtService.sign(payload);
      const refreshToken = this.generateRefreshToken(payload);

      this.logger.log(`Device authenticated: ${deviceId}`);

      return {
        access_token: accessToken,
        refresh_token: refreshToken,
        device: device.toSafeObject(),
        expires_in: this.getTokenExpirationTime(),
      };

    } catch (error) {
      this.logger.error(`Authentication failed for device ${deviceId}:`, error);
      throw new UnauthorizedException('Authentication failed');
    }
  }

  /**
   * Refresh access token
   */
  async refreshToken(refreshToken: string): Promise<AuthResult> {
    try {
      const payload = this.jwtService.verify(refreshToken, {
        secret: this.configService.get('JWT_REFRESH_SECRET'),
      });

      const device = await this.devicesService.findByDeviceId(payload.deviceId);

      // Update last activity
      await this.devicesService.updateLastActivity(payload.deviceId);

      // Generate new tokens
      const newPayload: JwtPayload = {
        sub: device.id,
        deviceId: device.deviceId,
        deviceType: device.deviceType,
      };

      const accessToken = this.jwtService.sign(newPayload);
      const newRefreshToken = this.generateRefreshToken(newPayload);

      this.logger.log(`Token refreshed for device: ${payload.deviceId}`);

      return {
        access_token: accessToken,
        refresh_token: newRefreshToken,
        device: device.toSafeObject(),
        expires_in: this.getTokenExpirationTime(),
      };

    } catch (error) {
      this.logger.error(`Token refresh failed:`, error);
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  /**
   * Validate JWT payload
   */
  async validateJwtPayload(payload: JwtPayload): Promise<Device> {
    try {
      const device = await this.devicesService.findByDeviceId(payload.deviceId);

      // Update last activity
      await this.devicesService.updateLastActivity(payload.deviceId);

      return device;

    } catch (error) {
      this.logger.error(`JWT validation failed for device ${payload.deviceId}:`, error);
      throw new UnauthorizedException('Invalid token');
    }
  }

  /**
   * Generate refresh token
   */
  private generateRefreshToken(payload: JwtPayload): string {
    return this.jwtService.sign(payload, {
      secret: this.configService.get('JWT_REFRESH_SECRET'),
      expiresIn: this.configService.get('JWT_REFRESH_EXPIRES_IN', '7d'),
    });
  }

  /**
   * Get token expiration time in seconds
   */
  private getTokenExpirationTime(): number {
    const expiresIn = this.configService.get('JWT_EXPIRES_IN', '24h');
    
    // Parse expiration time (simple parser for common formats)
    if (expiresIn.endsWith('h')) {
      return parseInt(expiresIn) * 3600;
    } else if (expiresIn.endsWith('d')) {
      return parseInt(expiresIn) * 86400;
    } else if (expiresIn.endsWith('m')) {
      return parseInt(expiresIn) * 60;
    } else {
      return parseInt(expiresIn); // Assume seconds
    }
  }

  /**
   * Revoke device tokens (logout)
   */
  async revokeDeviceTokens(deviceId: string): Promise<void> {
    // In a production system, you would maintain a blacklist of revoked tokens
    // or use a token store like Redis to track valid tokens
    this.logger.log(`Tokens revoked for device: ${deviceId}`);
  }

  /**
   * Generate API key for device (for server-to-server communication)
   */
  async generateApiKey(deviceId: string): Promise<string> {
    const device = await this.devicesService.findByDeviceId(deviceId);
    
    const payload = {
      sub: device.id,
      deviceId: device.deviceId,
      deviceType: device.deviceType,
      type: 'api_key',
    };

    // API keys don't expire
    return this.jwtService.sign(payload, { expiresIn: '10y' });
  }

  /**
   * Validate API key
   */
  async validateApiKey(apiKey: string): Promise<Device> {
    try {
      const payload = this.jwtService.verify(apiKey);
      
      if (payload.type !== 'api_key') {
        throw new UnauthorizedException('Invalid API key');
      }

      return await this.devicesService.findByDeviceId(payload.deviceId);

    } catch (error) {
      this.logger.error(`API key validation failed:`, error);
      throw new UnauthorizedException('Invalid API key');
    }
  }
}
