import { Controller, Post, Body, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';
import { AuthService, AuthResult } from './auth.service';
import { Public } from './decorators/public.decorator';

class AuthenticateDeviceDto {
  deviceId: string;
  sipUsername?: string;
  sipPassword?: string;
}

class RefreshTokenDto {
  refresh_token: string;
}

class GenerateApiKeyDto {
  deviceId: string;
}

@ApiTags('Authentication')
@Controller('auth')
export class AuthController {
  constructor(private readonly authService: AuthService) {}

  @Public()
  @Post('authenticate')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Authenticate device' })
  @ApiBody({ type: AuthenticateDeviceDto })
  @ApiResponse({ status: 200, description: 'Device authenticated successfully' })
  @ApiResponse({ status: 401, description: 'Authentication failed' })
  async authenticate(@Body() body: AuthenticateDeviceDto): Promise<AuthResult> {
    return await this.authService.authenticateDevice(
      body.deviceId,
      body.sipUsername,
      body.sipPassword,
    );
  }

  @Public()
  @Post('refresh')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Refresh access token' })
  @ApiBody({ type: RefreshTokenDto })
  @ApiResponse({ status: 200, description: 'Token refreshed successfully' })
  @ApiResponse({ status: 401, description: 'Invalid refresh token' })
  async refresh(@Body() body: RefreshTokenDto): Promise<AuthResult> {
    return await this.authService.refreshToken(body.refresh_token);
  }

  @Post('api-key')
  @ApiOperation({ summary: 'Generate API key for device' })
  @ApiBody({ type: GenerateApiKeyDto })
  @ApiResponse({ status: 200, description: 'API key generated successfully' })
  async generateApiKey(@Body() body: GenerateApiKeyDto): Promise<{ api_key: string }> {
    const apiKey = await this.authService.generateApiKey(body.deviceId);
    return { api_key: apiKey };
  }

  @Post('logout')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Logout device (revoke tokens)' })
  @ApiResponse({ status: 204, description: 'Tokens revoked successfully' })
  async logout(@Body() body: { deviceId: string }): Promise<void> {
    await this.authService.revokeDeviceTokens(body.deviceId);
  }
}
