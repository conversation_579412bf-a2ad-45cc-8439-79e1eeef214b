# VoIP Backend Service

A comprehensive NestJS backend service for managing VoIP calls between Android and iOS devices. This service handles device registration, call routing, push notifications, and real-time communication for a VoIP forwarding system.

## Features

- **Device Management**: Register and manage Android/iOS device pairs
- **VoIP Push Notifications**: Apple Push Notification service integration for VoIP calls
- **Call Management**: Complete call session management with quality monitoring
- **SIP/WebRTC Bridge**: Bridge calls between different VoIP protocols
- **Real-time Communication**: WebSocket gateway for call signaling
- **Authentication**: JWT-based authentication with refresh tokens
- **Database**: PostgreSQL with TypeORM for data persistence
- **Logging**: Comprehensive logging with Winston
- **API Documentation**: Swagger/OpenAPI documentation

## Quick Start

### Prerequisites

- Node.js 18+ 
- PostgreSQL 12+
- Redis (optional, for caching)
- Apple Developer Account (for VoIP push notifications)

### Installation

1. **Clone and install dependencies:**
```bash
git clone <repository-url>
cd Backend
npm install
```

2. **Configure environment:**
```bash
cp .env.example .env
# Edit .env with your configuration
```

3. **Setup database:**
```bash
# Create PostgreSQL database
createdb voip_db

# Run migrations (if any)
npm run migration:run
```

4. **Start development server:**
```bash
npm run start:dev
```

The API will be available at `http://localhost:3000`

### Docker Setup

1. **Using Docker Compose (recommended):**
```bash
# Copy environment file
cp .env.example .env

# Start all services
docker-compose up -d

# View logs
docker-compose logs -f voip-backend
```

2. **Build and run manually:**
```bash
# Build image
docker build -t voip-backend .

# Run container
docker run -d \
  --name voip-backend \
  -p 3000:3000 \
  -e DB_HOST=your-db-host \
  -e DB_PASSWORD=your-db-password \
  voip-backend
```

## Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NODE_ENV` | Environment (development/production) | `development` |
| `PORT` | Server port | `3000` |
| `DB_HOST` | PostgreSQL host | `localhost` |
| `DB_PORT` | PostgreSQL port | `5432` |
| `DB_USERNAME` | Database username | `voip_user` |
| `DB_PASSWORD` | Database password | `voip_password` |
| `DB_DATABASE` | Database name | `voip_db` |
| `JWT_SECRET` | JWT signing secret | Required |
| `APN_KEY_PATH` | Path to Apple Push Notification key file | Required for iOS |
| `APN_KEY_ID` | Apple Push Notification key ID | Required for iOS |
| `APN_TEAM_ID` | Apple Developer Team ID | Required for iOS |

### Apple Push Notifications Setup

1. **Create VoIP Services Certificate:**
   - Go to Apple Developer Console
   - Create a VoIP Services Certificate for your app
   - Download the `.p8` key file

2. **Configure environment:**
```bash
APN_KEY_PATH=/path/to/your/AuthKey_XXXXXXXXXX.p8
APN_KEY_ID=XXXXXXXXXX
APN_TEAM_ID=XXXXXXXXXX
IOS_BUNDLE_ID=com.yourapp.voipreceiver
```

## API Documentation

### Authentication

All API endpoints (except registration and authentication) require a Bearer token:

```bash
# Authenticate device
curl -X POST http://localhost:3000/auth/authenticate \
  -H "Content-Type: application/json" \
  -d '{"deviceId": "android-device-123", "sipUsername": "user", "sipPassword": "pass"}'

# Use token in subsequent requests
curl -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  http://localhost:3000/devices
```

### Key Endpoints

- **Device Management:**
  - `POST /devices/register` - Register device
  - `GET /devices` - List devices
  - `POST /devices/pair` - Pair Android/iOS devices

- **Call Management:**
  - `POST /calls/initiate` - Initiate call
  - `PUT /calls/:callId` - Update call status
  - `GET /calls/active` - Get active calls

- **Push Notifications:**
  - `POST /push/voip-call` - Send VoIP push
  - `GET /push/stats` - Push statistics

### WebSocket Events

Connect to `/webrtc` namespace for real-time communication:

```javascript
const socket = io('http://localhost:3000/webrtc', {
  auth: { token: 'YOUR_JWT_TOKEN' }
});

// Listen for incoming calls
socket.on('webrtc-signal', (data) => {
  console.log('WebRTC signal:', data);
});

// Send call initiation
socket.emit('initiate-call', {
  callerId: '+1234567890',
  callerName: 'John Doe'
});
```

## Development

### Available Scripts

```bash
# Development
npm run start:dev          # Start with hot reload
npm run start:debug        # Start with debugging

# Building
npm run build              # Build for production
npm run start:prod         # Start production build

# Testing
npm run test               # Run unit tests
npm run test:e2e           # Run end-to-end tests
npm run test:cov           # Run tests with coverage

# Database
npm run migration:generate # Generate migration
npm run migration:run      # Run migrations
npm run migration:revert   # Revert migration
```

### Project Structure

```
src/
├── auth/                  # Authentication module
├── calls/                 # Call management
├── common/                # Shared utilities
├── config/                # Configuration files
├── database/              # Database entities and migrations
├── devices/               # Device management
├── push/                  # Push notification service
├── webrtc/                # WebRTC/SIP bridge
├── app.module.ts          # Main application module
└── main.ts                # Application entry point
```

## Monitoring and Logging

### Health Check

```bash
curl http://localhost:3000/health
```

### Logs

Logs are written to both console and file:
- Development: Console with colors
- Production: File rotation with JSON format

```bash
# View logs in Docker
docker-compose logs -f voip-backend

# View log files
tail -f logs/app.log
```

### Metrics

Access Swagger documentation at: `http://localhost:3000/api`

## Deployment

### Production Checklist

- [ ] Set strong JWT secrets
- [ ] Configure proper database credentials
- [ ] Setup Apple Push Notification certificates
- [ ] Configure STUN/TURN servers for WebRTC
- [ ] Setup SSL/TLS certificates
- [ ] Configure firewall rules
- [ ] Setup monitoring and alerting
- [ ] Configure log rotation
- [ ] Setup database backups

### Environment-specific Configuration

```bash
# Production
NODE_ENV=production
JWT_SECRET=your-very-secure-secret-key
DB_PASSWORD=secure-database-password

# Staging
NODE_ENV=staging
LOG_LEVEL=debug
```

## Troubleshooting

### Common Issues

1. **Database Connection Failed:**
   - Check PostgreSQL is running
   - Verify connection credentials
   - Ensure database exists

2. **Push Notifications Not Working:**
   - Verify Apple Push Notification certificates
   - Check bundle ID matches
   - Ensure device tokens are valid

3. **WebRTC Connection Issues:**
   - Configure STUN/TURN servers
   - Check firewall settings
   - Verify WebSocket connections

### Debug Mode

```bash
# Enable debug logging
LOG_LEVEL=debug npm run start:dev

# Debug specific modules
DEBUG=voip:* npm run start:dev
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.
