# VoIP Backend Deployment Guide

This guide covers deploying the VoIP Backend service to various environments including Docker, cloud platforms, and on-premises servers.

## Prerequisites

- Docker and Docker Compose
- PostgreSQL 12+
- Node.js 18+ (for non-Docker deployments)
- SSL certificates for production
- Apple Developer Account (for VoIP push notifications)

## Environment Configuration

### 1. Environment Variables

Copy and configure the environment file:

```bash
cp .env.example .env
```

**Critical Production Settings:**

```bash
# Application
NODE_ENV=production
PORT=3000

# Security
JWT_SECRET=your-very-secure-random-secret-key-here
JWT_REFRESH_SECRET=another-very-secure-random-secret-key

# Database
DB_HOST=your-postgres-host
DB_PORT=5432
DB_USERNAME=voip_user
DB_PASSWORD=secure-database-password
DB_DATABASE=voip_db

# Apple Push Notifications (Required for iOS)
APN_KEY_PATH=/app/apn-keys/AuthKey_XXXXXXXXXX.p8
APN_KEY_ID=XXXXXXXXXX
APN_TEAM_ID=XXXXXXXXXX
IOS_BUNDLE_ID=com.yourcompany.voipreceiver

# WebRTC (Recommended for production)
STUN_SERVERS=stun:stun.l.google.com:19302,stun:stun1.l.google.com:19302
TURN_SERVERS=turn:your-turn-server.com:3478
TURN_USERNAME=your-turn-username
TURN_PASSWORD=your-turn-password
```

### 2. Apple Push Notification Setup

1. **Create VoIP Services Certificate:**
   - Go to [Apple Developer Console](https://developer.apple.com)
   - Navigate to Certificates, Identifiers & Profiles
   - Create a new Key for Apple Push Notifications service (APNs)
   - Download the `.p8` key file

2. **Configure the key:**
```bash
mkdir -p apn-keys
cp ~/Downloads/AuthKey_XXXXXXXXXX.p8 apn-keys/
chmod 600 apn-keys/AuthKey_XXXXXXXXXX.p8
```

## Docker Deployment (Recommended)

### 1. Using Docker Compose

**Production docker-compose.yml:**

```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: voip_db
      POSTGRES_USER: voip_user
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    networks:
      - voip-network

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - voip-network
    command: redis-server --appendonly yes

  voip-backend:
    build: .
    environment:
      NODE_ENV: production
      DB_HOST: postgres
      REDIS_HOST: redis
      JWT_SECRET: ${JWT_SECRET}
      APN_KEY_PATH: /app/apn-keys/AuthKey_${APN_KEY_ID}.p8
      APN_KEY_ID: ${APN_KEY_ID}
      APN_TEAM_ID: ${APN_TEAM_ID}
    volumes:
      - ./apn-keys:/app/apn-keys:ro
      - ./logs:/app/logs
    ports:
      - "3000:3000"
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    networks:
      - voip-network

volumes:
  postgres_data:
  redis_data:

networks:
  voip-network:
```

**Deploy:**

```bash
# Build and start services
docker-compose up -d

# View logs
docker-compose logs -f voip-backend

# Check status
docker-compose ps
```

### 2. Single Container Deployment

```bash
# Build image
docker build -t voip-backend .

# Run with external database
docker run -d \
  --name voip-backend \
  -p 3000:3000 \
  -e NODE_ENV=production \
  -e DB_HOST=your-db-host \
  -e DB_PASSWORD=your-db-password \
  -e JWT_SECRET=your-jwt-secret \
  -v $(pwd)/apn-keys:/app/apn-keys:ro \
  -v $(pwd)/logs:/app/logs \
  voip-backend
```

## Cloud Platform Deployment

### 1. AWS ECS with Fargate

**task-definition.json:**

```json
{
  "family": "voip-backend",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "512",
  "memory": "1024",
  "executionRoleArn": "arn:aws:iam::ACCOUNT:role/ecsTaskExecutionRole",
  "taskRoleArn": "arn:aws:iam::ACCOUNT:role/ecsTaskRole",
  "containerDefinitions": [
    {
      "name": "voip-backend",
      "image": "your-account.dkr.ecr.region.amazonaws.com/voip-backend:latest",
      "portMappings": [
        {
          "containerPort": 3000,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {"name": "NODE_ENV", "value": "production"},
        {"name": "PORT", "value": "3000"}
      ],
      "secrets": [
        {"name": "JWT_SECRET", "valueFrom": "arn:aws:secretsmanager:region:account:secret:voip/jwt-secret"},
        {"name": "DB_PASSWORD", "valueFrom": "arn:aws:secretsmanager:region:account:secret:voip/db-password"}
      ],
      "logConfiguration": {
        "logDriver": "awslogs",
        "options": {
          "awslogs-group": "/ecs/voip-backend",
          "awslogs-region": "us-east-1",
          "awslogs-stream-prefix": "ecs"
        }
      }
    }
  ]
}
```

### 2. Google Cloud Run

**Deploy to Cloud Run:**

```bash
# Build and push to Container Registry
gcloud builds submit --tag gcr.io/PROJECT-ID/voip-backend

# Deploy to Cloud Run
gcloud run deploy voip-backend \
  --image gcr.io/PROJECT-ID/voip-backend \
  --platform managed \
  --region us-central1 \
  --allow-unauthenticated \
  --set-env-vars NODE_ENV=production \
  --set-env-vars DB_HOST=your-cloud-sql-ip \
  --memory 1Gi \
  --cpu 1 \
  --max-instances 10
```

### 3. Azure Container Instances

```bash
# Create resource group
az group create --name voip-rg --location eastus

# Deploy container
az container create \
  --resource-group voip-rg \
  --name voip-backend \
  --image your-registry/voip-backend:latest \
  --cpu 1 \
  --memory 1 \
  --ports 3000 \
  --environment-variables \
    NODE_ENV=production \
    PORT=3000 \
  --secure-environment-variables \
    JWT_SECRET=your-jwt-secret \
    DB_PASSWORD=your-db-password
```

## Kubernetes Deployment

### 1. Deployment Manifest

**k8s/deployment.yaml:**

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: voip-backend
  labels:
    app: voip-backend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: voip-backend
  template:
    metadata:
      labels:
        app: voip-backend
    spec:
      containers:
      - name: voip-backend
        image: voip-backend:latest
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: "production"
        - name: PORT
          value: "3000"
        - name: DB_HOST
          value: "postgres-service"
        - name: REDIS_HOST
          value: "redis-service"
        envFrom:
        - secretRef:
            name: voip-secrets
        volumeMounts:
        - name: apn-keys
          mountPath: /app/apn-keys
          readOnly: true
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 5
          periodSeconds: 5
      volumes:
      - name: apn-keys
        secret:
          secretName: apn-keys
---
apiVersion: v1
kind: Service
metadata:
  name: voip-backend-service
spec:
  selector:
    app: voip-backend
  ports:
  - port: 80
    targetPort: 3000
  type: LoadBalancer
```

### 2. Secrets Management

```bash
# Create secrets
kubectl create secret generic voip-secrets \
  --from-literal=JWT_SECRET=your-jwt-secret \
  --from-literal=DB_PASSWORD=your-db-password \
  --from-literal=APN_KEY_ID=your-apn-key-id \
  --from-literal=APN_TEAM_ID=your-team-id

# Create APN key secret
kubectl create secret generic apn-keys \
  --from-file=AuthKey_XXXXXXXXXX.p8=./apn-keys/AuthKey_XXXXXXXXXX.p8

# Deploy
kubectl apply -f k8s/
```

## Database Setup

### 1. PostgreSQL Configuration

**For production, configure PostgreSQL with:**

```sql
-- Create database and user
CREATE DATABASE voip_db;
CREATE USER voip_user WITH ENCRYPTED PASSWORD 'secure-password';
GRANT ALL PRIVILEGES ON DATABASE voip_db TO voip_user;

-- Performance tuning (adjust based on your server)
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;
SELECT pg_reload_conf();
```

### 2. Database Migrations

```bash
# Run migrations on deployment
npm run migration:run

# Or in Docker
docker exec voip-backend npm run migration:run
```

## SSL/TLS Configuration

### 1. Nginx Reverse Proxy

**nginx.conf:**

```nginx
upstream voip-backend {
    server voip-backend:3000;
}

server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;

    location / {
        proxy_pass http://voip-backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # WebSocket support
    location /socket.io/ {
        proxy_pass http://voip-backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## Monitoring and Logging

### 1. Health Checks

```bash
# Basic health check
curl https://your-domain.com/health

# Detailed monitoring
curl https://your-domain.com/api/v1/devices/stats
curl https://your-domain.com/api/v1/calls/stats
curl https://your-domain.com/api/v1/push/stats
```

### 2. Log Aggregation

**For centralized logging with ELK stack:**

```yaml
# docker-compose.yml addition
  filebeat:
    image: docker.elastic.co/beats/filebeat:7.15.0
    volumes:
      - ./logs:/var/log/app:ro
      - ./filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
    depends_on:
      - voip-backend
```

## Security Checklist

- [ ] Use strong JWT secrets (32+ characters)
- [ ] Enable HTTPS/TLS in production
- [ ] Configure firewall rules
- [ ] Use environment variables for secrets
- [ ] Enable database SSL connections
- [ ] Implement rate limiting
- [ ] Regular security updates
- [ ] Monitor for suspicious activity
- [ ] Backup encryption keys
- [ ] Use least privilege access

## Backup and Recovery

### 1. Database Backup

```bash
# Automated backup script
#!/bin/bash
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)

pg_dump -h $DB_HOST -U $DB_USERNAME $DB_DATABASE > $BACKUP_DIR/voip_db_$DATE.sql
gzip $BACKUP_DIR/voip_db_$DATE.sql

# Keep only last 7 days
find $BACKUP_DIR -name "voip_db_*.sql.gz" -mtime +7 -delete
```

### 2. Configuration Backup

```bash
# Backup critical files
tar -czf config_backup_$(date +%Y%m%d).tar.gz \
  .env \
  apn-keys/ \
  docker-compose.yml \
  nginx/
```

## Troubleshooting

### Common Issues

1. **Database Connection Failed:**
   ```bash
   # Check database connectivity
   docker exec voip-backend pg_isready -h postgres -p 5432
   ```

2. **Push Notifications Not Working:**
   ```bash
   # Verify APN key file
   docker exec voip-backend ls -la /app/apn-keys/
   
   # Check logs for APN errors
   docker logs voip-backend | grep -i apn
   ```

3. **High Memory Usage:**
   ```bash
   # Monitor container resources
   docker stats voip-backend
   
   # Adjust memory limits in docker-compose.yml
   ```

### Performance Tuning

1. **Database Optimization:**
   - Enable connection pooling
   - Add database indexes
   - Configure query optimization

2. **Application Scaling:**
   - Use multiple container instances
   - Implement load balancing
   - Enable Redis caching

3. **Network Optimization:**
   - Configure CDN for static assets
   - Enable gzip compression
   - Optimize WebSocket connections
